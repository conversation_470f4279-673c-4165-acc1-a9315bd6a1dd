#!/usr/bin/env python3
"""
Check current database structure
"""

from app import app, db
from sqlalchemy import text

def check_database_structure():
    """Check current database table structure"""
    
    with app.app_context():
        try:
            print("Checking current database structure...")
            
            # Check table structure
            with db.engine.connect() as connection:
                result = connection.execute(text("DESCRIBE dicom_studies"))
                columns = result.fetchall()
                
                print("\nCurrent columns in dicom_studies table:")
                print("=" * 50)
                for column in columns:
                    print(f"{column[0]:<30} {column[1]:<20} {column[2]}")
                
                print(f"\nTotal columns: {len(columns)}")
                
                # Check if patient info columns exist
                column_names = [col[0] for col in columns]
                patient_columns = [
                    'patient_name', 'patient_birth_date', 'patient_sex', 
                    'patient_weight', 'performing_physician', 'requesting_physician',
                    'study_time', 'accession_number', 'study_id_dicom'
                ]
                
                print("\nPatient information columns status:")
                print("=" * 40)
                for col in patient_columns:
                    status = "✓ EXISTS" if col in column_names else "✗ MISSING"
                    print(f"{col:<25} {status}")
                
        except Exception as e:
            print(f"Error checking database structure: {str(e)}")
            return False
            
    return True

if __name__ == '__main__':
    check_database_structure()
