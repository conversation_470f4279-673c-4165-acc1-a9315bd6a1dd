#!/usr/bin/env python3
"""
Create admin user and setup user management system
"""

from app import app, db
from models import User
from datetime import datetime

def create_admin_user():
    """Create the initial admin user"""
    
    with app.app_context():
        try:
            # Create all tables
            db.create_all()
            print("✓ Database tables created successfully")
            
            # Check if admin user already exists
            admin_user = User.query.filter_by(username='admin').first()
            
            if admin_user:
                print("ℹ Admin user already exists")
                print(f"  Username: {admin_user.username}")
                print(f"  Email: {admin_user.email}")
                print(f"  Name: {admin_user.name}")
                print(f"  Role: {admin_user.user_type}")
                return admin_user
            
            # Create admin user
            admin_user = User(
                name='Administrator',
                username='admin',
                email='<EMAIL>',
                user_type='admin',
                is_active=True
            )
            admin_user.set_password('admin123')  # Change this password!
            
            db.session.add(admin_user)
            db.session.commit()
            
            print("🎉 Admin user created successfully!")
            print("=" * 50)
            print("Default Admin Credentials:")
            print("Username: admin")
            print("Password: admin123")
            print("Email: <EMAIL>")
            print("=" * 50)
            print("⚠️  IMPORTANT: Please change the default password after first login!")
            
            return admin_user
            
        except Exception as e:
            print(f"❌ Error creating admin user: {str(e)}")
            db.session.rollback()
            return None

def create_sample_users():
    """Create some sample users for testing"""
    
    with app.app_context():
        try:
            # Check if sample users already exist
            if User.query.filter_by(username='doctor1').first():
                print("ℹ Sample users already exist")
                return
            
            # Create sample doctor user
            doctor_user = User(
                name='Dr. John Smith',
                username='doctor1',
                email='<EMAIL>',
                user_type='user',
                is_active=True
            )
            doctor_user.set_password('doctor123')
            
            # Create sample radiologist user
            radiologist_user = User(
                name='Dr. Sarah Johnson',
                username='radiologist1',
                email='<EMAIL>',
                user_type='user',
                is_active=True
            )
            radiologist_user.set_password('radio123')
            
            db.session.add(doctor_user)
            db.session.add(radiologist_user)
            db.session.commit()
            
            print("✓ Sample users created successfully!")
            print("Sample User Credentials:")
            print("1. Doctor - Username: doctor1, Password: doctor123")
            print("2. Radiologist - Username: radiologist1, Password: radio123")
            
        except Exception as e:
            print(f"❌ Error creating sample users: {str(e)}")
            db.session.rollback()

def show_user_stats():
    """Show current user statistics"""
    
    with app.app_context():
        try:
            total_users = User.query.count()
            admin_users = User.query.filter_by(user_type='admin').count()
            regular_users = User.query.filter_by(user_type='user').count()
            active_users = User.query.filter_by(is_active=True).count()
            
            print("\n📊 User Statistics:")
            print(f"Total Users: {total_users}")
            print(f"Admin Users: {admin_users}")
            print(f"Regular Users: {regular_users}")
            print(f"Active Users: {active_users}")
            
            print("\n👥 All Users:")
            users = User.query.all()
            for user in users:
                status = "✓ Active" if user.is_active else "✗ Inactive"
                role = "🛡️ Admin" if user.is_admin() else "👤 User"
                print(f"  {role} {user.name} (@{user.username}) - {status}")
                
        except Exception as e:
            print(f"❌ Error getting user stats: {str(e)}")

if __name__ == '__main__':
    print("🚀 Setting up User Management System...")
    print("=" * 60)
    
    # Create admin user
    admin_user = create_admin_user()
    
    if admin_user:
        # Create sample users
        create_sample_users()
        
        # Show statistics
        show_user_stats()
        
        print("\n✅ User management system setup complete!")
        print("\nNext steps:")
        print("1. Start the Flask application: python app.py")
        print("2. Login with admin credentials")
        print("3. Change the default admin password")
        print("4. Create additional users as needed")
    else:
        print("❌ Failed to setup user management system")
