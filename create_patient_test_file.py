#!/usr/bin/env python3
"""
Create a test DICOM file with clear patient information to verify display
"""

import os
import pydicom
from pydicom.dataset import Dataset, FileDataset
from pydicom.uid import generate_uid
import numpy as np
from datetime import datetime

def create_patient_test_dicom():
    """Create a DICOM file with clear patient information"""
    
    # Create test files directory if it doesn't exist
    os.makedirs("test_files", exist_ok=True)
    
    # Create a new DICOM dataset
    file_meta = Dataset()
    file_meta.MediaStorageSOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
    file_meta.MediaStorageSOPInstanceUID = generate_uid()
    file_meta.ImplementationClassUID = generate_uid()
    file_meta.TransferSyntaxUID = "1.2.840.10008.1.2"  # Implicit VR Little Endian
    
    # Create the main dataset
    ds = FileDataset("patient_test.dcm", {}, file_meta=file_meta, preamble=b"\0" * 128)
    
    # Patient Information (This is what we want to see displayed)
    ds.PatientName = "AHMED HASSAN MOHAMED"
    ds.PatientID = "P123456"
    ds.PatientBirthDate = "19850315"
    ds.PatientSex = "M"
    
    # Study Information
    ds.StudyInstanceUID = generate_uid()
    ds.StudyID = "ST001"
    ds.StudyDate = datetime.now().strftime("%Y%m%d")
    ds.StudyTime = datetime.now().strftime("%H%M%S")
    ds.StudyDescription = "CT Chest with Contrast"
    ds.AccessionNumber = "ACC123456"
    
    # Series Information
    ds.SeriesInstanceUID = generate_uid()
    ds.SeriesNumber = "1"
    ds.SeriesDate = datetime.now().strftime("%Y%m%d")
    ds.SeriesTime = datetime.now().strftime("%H%M%S")
    ds.SeriesDescription = "Axial CT Images"
    ds.Modality = "CT"
    
    # Instance Information
    ds.SOPInstanceUID = generate_uid()
    ds.SOPClassUID = "1.2.840.10008.*******.1.2"
    ds.InstanceNumber = "1"
    
    # Image Information
    ds.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL"]
    ds.SamplesPerPixel = 1
    ds.PhotometricInterpretation = "MONOCHROME2"
    ds.Rows = 512
    ds.Columns = 512
    ds.BitsAllocated = 16
    ds.BitsStored = 16
    ds.HighBit = 15
    ds.PixelRepresentation = 0
    
    # Institution Information
    ds.InstitutionName = "Shaafi Hospital"
    ds.InstitutionAddress = "Mogadishu, Somalia"
    ds.Manufacturer = "Test Equipment"
    ds.ManufacturerModelName = "Test Model"
    
    # Create pixel data (simple gradient pattern)
    pixel_array = np.zeros((512, 512), dtype=np.uint16)
    for i in range(512):
        for j in range(512):
            pixel_array[i, j] = (i + j) % 4096
    
    ds.PixelData = pixel_array.tobytes()
    
    # Save the file
    filename = "test_files/patient_test_ahmed.dcm"
    ds.save_as(filename)
    
    print(f"✅ Created test DICOM file: {filename}")
    print(f"   Patient Name: {ds.PatientName}")
    print(f"   Patient ID: {ds.PatientID}")
    print(f"   Study Description: {ds.StudyDescription}")
    print(f"   Modality: {ds.Modality}")
    
    return filename

def create_another_patient_test():
    """Create another test file with different patient"""
    
    # Create a new DICOM dataset
    file_meta = Dataset()
    file_meta.MediaStorageSOPClassUID = "1.2.840.10008.*******.1.1"  # CR Image Storage
    file_meta.MediaStorageSOPInstanceUID = generate_uid()
    file_meta.ImplementationClassUID = generate_uid()
    file_meta.TransferSyntaxUID = "1.2.840.10008.1.2"
    
    ds = FileDataset("patient_test2.dcm", {}, file_meta=file_meta, preamble=b"\0" * 128)
    
    # Different Patient Information
    ds.PatientName = "FATIMA ALI OMAR"
    ds.PatientID = "P789012"
    ds.PatientBirthDate = "19920708"
    ds.PatientSex = "F"
    
    # Study Information
    ds.StudyInstanceUID = generate_uid()
    ds.StudyID = "ST002"
    ds.StudyDate = datetime.now().strftime("%Y%m%d")
    ds.StudyTime = datetime.now().strftime("%H%M%S")
    ds.StudyDescription = "Chest X-Ray PA and Lateral"
    ds.AccessionNumber = "ACC789012"
    
    # Series Information
    ds.SeriesInstanceUID = generate_uid()
    ds.SeriesNumber = "1"
    ds.SeriesDate = datetime.now().strftime("%Y%m%d")
    ds.SeriesTime = datetime.now().strftime("%H%M%S")
    ds.SeriesDescription = "PA Chest"
    ds.Modality = "CR"
    
    # Instance Information
    ds.SOPInstanceUID = generate_uid()
    ds.SOPClassUID = "1.2.840.10008.*******.1.1"
    ds.InstanceNumber = "1"
    
    # Image Information
    ds.ImageType = ["ORIGINAL", "PRIMARY"]
    ds.SamplesPerPixel = 1
    ds.PhotometricInterpretation = "MONOCHROME2"
    ds.Rows = 2048
    ds.Columns = 2048
    ds.BitsAllocated = 16
    ds.BitsStored = 16
    ds.HighBit = 15
    ds.PixelRepresentation = 0
    
    # Institution Information
    ds.InstitutionName = "Shaafi Hospital"
    ds.InstitutionAddress = "Mogadishu, Somalia"
    ds.Manufacturer = "Test Equipment"
    ds.ManufacturerModelName = "Test Model"
    
    # Create pixel data
    pixel_array = np.zeros((2048, 2048), dtype=np.uint16)
    for i in range(2048):
        for j in range(2048):
            pixel_array[i, j] = (i * j) % 4096
    
    ds.PixelData = pixel_array.tobytes()
    
    # Save the file
    filename = "test_files/patient_test_fatima.dcm"
    ds.save_as(filename)
    
    print(f"✅ Created test DICOM file: {filename}")
    print(f"   Patient Name: {ds.PatientName}")
    print(f"   Patient ID: {ds.PatientID}")
    print(f"   Study Description: {ds.StudyDescription}")
    print(f"   Modality: {ds.Modality}")
    
    return filename

def main():
    """Create test DICOM files with clear patient information"""
    print("👤 CREATING PATIENT TEST FILES")
    print("=" * 50)
    
    try:
        # Create test files
        file1 = create_patient_test_dicom()
        file2 = create_another_patient_test()
        
        print(f"\n🎉 Successfully created test files!")
        print(f"\n📋 Test Files Created:")
        print(f"  • {file1}")
        print(f"  • {file2}")
        
        print(f"\n📋 What to Test:")
        print(f"  1. Upload these files via the web interface")
        print(f"  2. Check that patient names are displayed instead of 'Untitled Study'")
        print(f"  3. Verify the display shows: 'AHMED HASSAN MOHAMED (ID: P123456)'")
        print(f"  4. Verify the display shows: 'FATIMA ALI OMAR (ID: P789012)'")
        
        print(f"\n🌐 Upload at: http://127.0.0.1:5000/upload")
        print(f"🔐 Login as: admin / admin123")
        
    except Exception as e:
        print(f"❌ Error creating test files: {str(e)}")

if __name__ == '__main__':
    main()
