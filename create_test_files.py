#!/usr/bin/env python3
"""
Create test files for multi-file upload testing
"""

import os
import zipfile
import tempfile
import numpy as np
from datetime import datetime

def create_test_files():
    """Create real DICOM test files and ZIP archive"""

    print("🔧 Creating real DICOM test files for multi-file upload...")

    # Create test directory
    test_dir = "test_files"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)

    try:
        import pydicom
        from pydicom.dataset import Dataset, FileDataset
        from pydicom.uid import ExplicitVRLittleEndian
        import pydicom.uid

        print("✅ Using pydicom to create real DICOM files...")

        # Create sample DICOM files
        sample_files = [
            "sample_image_001.dcm",
            "sample_image_002.dcm",
            "sample_image_003.dcm",
            "sample_scan_001.dicom",
            "sample_scan_002.dicom"
        ]

        created_files = []

        for i, filename in enumerate(sample_files, 1):
            # Create a simple DICOM dataset
            file_meta = Dataset()
            file_meta.MediaStorageSOPClassUID = pydicom.uid.CTImageStorage
            file_meta.MediaStorageSOPInstanceUID = pydicom.uid.generate_uid()
            file_meta.ImplementationClassUID = pydicom.uid.generate_uid()
            file_meta.TransferSyntaxUID = ExplicitVRLittleEndian

            # Create the main dataset
            ds = FileDataset(filename, {}, file_meta=file_meta, preamble=b"\0" * 128)

            # Add required DICOM elements
            ds.PatientName = f"Test^Patient^{i}"
            ds.PatientID = f"TEST{i:03d}"
            ds.PatientBirthDate = "19800101"
            ds.PatientSex = "M" if i % 2 else "F"
            ds.StudyInstanceUID = pydicom.uid.generate_uid()
            ds.SeriesInstanceUID = pydicom.uid.generate_uid()
            ds.SOPInstanceUID = file_meta.MediaStorageSOPInstanceUID
            ds.SOPClassUID = file_meta.MediaStorageSOPClassUID
            ds.StudyDate = datetime.now().strftime('%Y%m%d')
            ds.StudyTime = datetime.now().strftime('%H%M%S')
            ds.StudyDescription = f"Test Study Series {i}"
            ds.SeriesDescription = f"Test Series {i}"
            ds.Modality = "CT"
            ds.InstitutionName = "Test Hospital"
            ds.Manufacturer = "Test Manufacturer"
            ds.SeriesNumber = i
            ds.InstanceNumber = i

            # Create a simple test image (64x64 pixels)
            image_size = 64
            test_image = np.random.randint(0, 4096, (image_size, image_size), dtype=np.uint16)

            ds.Rows = image_size
            ds.Columns = image_size
            ds.BitsAllocated = 16
            ds.BitsStored = 12
            ds.HighBit = 11
            ds.PixelRepresentation = 0
            ds.SamplesPerPixel = 1
            ds.PhotometricInterpretation = "MONOCHROME2"
            ds.PixelData = test_image.tobytes()

            # Save the DICOM file
            filepath = os.path.join(test_dir, filename)
            ds.save_as(filepath)
            created_files.append(filepath)
            print(f"✅ Created real DICOM: {filepath}")

    except ImportError:
        print("⚠️  pydicom not available, creating minimal DICOM-like files...")

        # Fallback: Create minimal DICOM-like files
        sample_files = [
            "sample_image_001.dcm",
            "sample_image_002.dcm",
            "sample_image_003.dcm",
            "sample_scan_001.dicom",
            "sample_scan_002.dicom"
        ]

        # Create minimal DICOM header with proper structure
        def create_minimal_dicom(patient_name, patient_id, series_num):
            # DICOM preamble (128 bytes) + DICM prefix
            preamble = b'\x00' * 128 + b'DICM'

            # Minimal DICOM data elements
            data = preamble

            # Add some basic DICOM tags (simplified)
            # Patient Name (0010,0010)
            data += b'\x10\x00\x10\x00PN\x00\x10\x00' + patient_name.ljust(16, ' ').encode()[:16]

            # Patient ID (0010,0020)
            data += b'\x10\x00\x20\x00LO\x00\x08\x00' + patient_id.ljust(8, ' ').encode()[:8]

            # Modality (0008,0060)
            data += b'\x08\x00\x60\x00CS\x00\x02\x00CT'

            # Series Number (0020,0011)
            data += b'\x20\x00\x11\x00IS\x00\x02\x00' + f'{series_num:02d}'.encode()

            return data

        created_files = []

        for i, filename in enumerate(sample_files, 1):
            filepath = os.path.join(test_dir, filename)
            dicom_data = create_minimal_dicom(f"Test Patient {i}", f"TEST{i:03d}", i)

            with open(filepath, 'wb') as f:
                f.write(dicom_data)
            created_files.append(filepath)
            print(f"✅ Created minimal DICOM: {filepath}")

    # Create ZIP archive
    zip_path = os.path.join(test_dir, "dicom_series.zip")
    with zipfile.ZipFile(zip_path, 'w') as zipf:
        for filepath in created_files:
            zipf.write(filepath, os.path.basename(filepath))

    print(f"✅ Created ZIP archive: {zip_path}")

    # Create individual test files
    single_files = [
        "single_test.dcm",
        "another_test.dicom"
    ]

    for i, filename in enumerate(single_files, len(created_files) + 1):
        filepath = os.path.join(test_dir, filename)

        try:
            # Try to create with pydicom if available
            if 'pydicom' in locals():
                # Create another real DICOM file
                file_meta = Dataset()
                file_meta.MediaStorageSOPClassUID = pydicom.uid.CTImageStorage
                file_meta.MediaStorageSOPInstanceUID = pydicom.uid.generate_uid()
                file_meta.ImplementationClassUID = pydicom.uid.generate_uid()
                file_meta.TransferSyntaxUID = ExplicitVRLittleEndian

                ds = FileDataset(filename, {}, file_meta=file_meta, preamble=b"\0" * 128)

                ds.PatientName = f"Single^Test^{i}"
                ds.PatientID = f"SINGLE{i:03d}"
                ds.PatientBirthDate = "19850101"
                ds.PatientSex = "F" if i % 2 else "M"
                ds.StudyInstanceUID = pydicom.uid.generate_uid()
                ds.SeriesInstanceUID = pydicom.uid.generate_uid()
                ds.SOPInstanceUID = file_meta.MediaStorageSOPInstanceUID
                ds.SOPClassUID = file_meta.MediaStorageSOPClassUID
                ds.StudyDate = datetime.now().strftime('%Y%m%d')
                ds.StudyTime = datetime.now().strftime('%H%M%S')
                ds.StudyDescription = f"Single Test Study {i}"
                ds.SeriesDescription = f"Single Test Series {i}"
                ds.Modality = "CT"
                ds.InstitutionName = "Test Hospital"
                ds.Manufacturer = "Test Manufacturer"
                ds.SeriesNumber = i
                ds.InstanceNumber = i

                # Create test image
                image_size = 64
                test_image = np.random.randint(0, 4096, (image_size, image_size), dtype=np.uint16)

                ds.Rows = image_size
                ds.Columns = image_size
                ds.BitsAllocated = 16
                ds.BitsStored = 12
                ds.HighBit = 11
                ds.PixelRepresentation = 0
                ds.SamplesPerPixel = 1
                ds.PhotometricInterpretation = "MONOCHROME2"
                ds.PixelData = test_image.tobytes()

                ds.save_as(filepath)
            else:
                # Fallback to minimal DICOM
                dicom_data = create_minimal_dicom(f"Single Test {i}", f"SINGLE{i:03d}", i)
                with open(filepath, 'wb') as f:
                    f.write(dicom_data)

        except Exception as e:
            print(f"⚠️  Error creating {filename}: {e}")
            # Create minimal fallback
            dicom_data = create_minimal_dicom(f"Single Test {i}", f"SINGLE{i:03d}", i)
            with open(filepath, 'wb') as f:
                f.write(dicom_data)

        print(f"✅ Created single file: {filepath}")

    print(f"\n📁 Test files created in: {os.path.abspath(test_dir)}")
    print("\n📋 Files available for testing:")
    print("  • Individual DICOM files:")
    for filename in single_files:
        print(f"    - {filename}")
    print("  • ZIP archive with multiple files:")
    print(f"    - dicom_series.zip (contains {len(sample_files)} files)")

    print(f"\n🧪 Test Instructions:")
    print("1. Go to http://127.0.0.1:5000/upload")
    print("2. Try uploading individual .dcm files")
    print("3. Try uploading the dicom_series.zip file")
    print("4. Test drag and drop functionality")
    print("5. Check if gallery view appears for ZIP uploads")

if __name__ == '__main__':
    create_test_files()
