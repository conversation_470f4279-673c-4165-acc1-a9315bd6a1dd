#!/usr/bin/env python3
"""
Create test files for multi-file upload testing
"""

import os
import zipfile
import tempfile

def create_test_files():
    """Create test DICOM-like files and ZIP archive"""
    
    print("🔧 Creating test files for multi-file upload...")
    
    # Create test directory
    test_dir = "test_files"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # Create sample DICOM-like files (these won't be real DICOM but will test the upload flow)
    sample_files = [
        "sample_image_001.dcm",
        "sample_image_002.dcm", 
        "sample_image_003.dcm",
        "sample_scan_001.dicom",
        "sample_scan_002.dicom"
    ]
    
    # Create sample file content (minimal DICOM-like header)
    sample_content = b"DICM" + b"\x00" * 128 + b"Sample DICOM-like content for testing"
    
    created_files = []
    
    for filename in sample_files:
        filepath = os.path.join(test_dir, filename)
        with open(filepath, 'wb') as f:
            f.write(sample_content)
        created_files.append(filepath)
        print(f"✅ Created: {filepath}")
    
    # Create ZIP archive
    zip_path = os.path.join(test_dir, "dicom_series.zip")
    with zipfile.ZipFile(zip_path, 'w') as zipf:
        for filepath in created_files:
            zipf.write(filepath, os.path.basename(filepath))
    
    print(f"✅ Created ZIP archive: {zip_path}")
    
    # Create individual test files
    single_files = [
        "single_test.dcm",
        "another_test.dicom"
    ]
    
    for filename in single_files:
        filepath = os.path.join(test_dir, filename)
        with open(filepath, 'wb') as f:
            f.write(sample_content)
        print(f"✅ Created single file: {filepath}")
    
    print(f"\n📁 Test files created in: {os.path.abspath(test_dir)}")
    print("\n📋 Files available for testing:")
    print("  • Individual DICOM files:")
    for filename in single_files:
        print(f"    - {filename}")
    print("  • ZIP archive with multiple files:")
    print(f"    - dicom_series.zip (contains {len(sample_files)} files)")
    
    print(f"\n🧪 Test Instructions:")
    print("1. Go to http://127.0.0.1:5000/upload")
    print("2. Try uploading individual .dcm files")
    print("3. Try uploading the dicom_series.zip file")
    print("4. Test drag and drop functionality")
    print("5. Check if gallery view appears for ZIP uploads")

if __name__ == '__main__':
    create_test_files()
