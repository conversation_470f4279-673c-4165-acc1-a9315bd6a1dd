#!/usr/bin/env python3
"""
Debug script to check the actual database structure that <PERSON><PERSON><PERSON> is using
"""

from app import app, db
from sqlalchemy import text

def debug_database():
    """Debug the actual database structure"""
    
    with app.app_context():
        try:
            print("=" * 60)
            print("DATABASE DEBUG INFORMATION")
            print("=" * 60)
            print(f"Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
            print()
            
            # Check if we can connect
            with db.engine.connect() as connection:
                print("✓ Successfully connected to database")
                
                # Check what tables exist
                print("\nChecking tables...")
                result = connection.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                print(f"Tables found: {tables}")
                
                if 'dicom_studies' not in tables:
                    print("❌ ERROR: dicom_studies table does not exist!")
                    return False
                
                # Check the actual structure of dicom_studies table
                print(f"\nChecking structure of dicom_studies table...")
                result = connection.execute(text("DESCRIBE dicom_studies"))
                columns = result.fetchall()
                
                print(f"\nActual columns in dicom_studies table:")
                print("-" * 50)
                column_names = []
                for column in columns:
                    column_names.append(column[0])
                    print(f"{column[0]:<30} {column[1]:<20} {column[2]}")
                
                print(f"\nTotal columns: {len(columns)}")
                
                # Check specifically for patient_id column
                print(f"\nChecking for patient_id column...")
                if 'patient_id' in column_names:
                    print("✓ patient_id column EXISTS")
                else:
                    print("❌ patient_id column MISSING")
                
                # Check for all required patient columns
                required_patient_columns = [
                    'patient_id', 'patient_name', 'patient_birth_date', 
                    'patient_sex', 'patient_age', 'patient_weight',
                    'referring_physician', 'performing_physician', 
                    'requesting_physician', 'study_time', 'accession_number',
                    'study_id_dicom'
                ]
                
                print(f"\nChecking required patient columns:")
                print("-" * 40)
                missing_columns = []
                for col in required_patient_columns:
                    if col in column_names:
                        print(f"✓ {col}")
                    else:
                        print(f"❌ {col} - MISSING")
                        missing_columns.append(col)
                
                if missing_columns:
                    print(f"\n❌ MISSING COLUMNS: {missing_columns}")
                    print("This explains why the Flask app is failing!")
                    return False
                else:
                    print(f"\n✅ All required columns are present!")
                    return True
                
        except Exception as e:
            print(f"❌ Database connection error: {e}")
            return False

if __name__ == '__main__':
    debug_database()
