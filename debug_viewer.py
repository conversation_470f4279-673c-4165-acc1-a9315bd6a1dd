import requests
import os

# Test the API endpoints
study_uid = "50ea642e-d662-4c2d-b4ac-b489f81c7ed8"

# Test DICOM data endpoint
url1 = f"http://127.0.0.1:5000/api/dicom-data/{study_uid}"
print(f"Testing DICOM data API endpoint: {url1}")

try:
    response = requests.get(url1)
    print(f"Status Code: {response.status_code}")
    print(f"Content-Type: {response.headers.get('Content-Type')}")
    print(f"Content-Length: {len(response.content)}")

    if response.status_code != 200:
        print(f"Error: {response.text}")
    else:
        print("DICOM data API endpoint is working correctly")

except Exception as e:
    print(f"Error: {e}")

print("\n" + "="*50 + "\n")

# Test DICOM image endpoint
url2 = f"http://127.0.0.1:5000/api/dicom-image/{study_uid}"
print(f"Testing DICOM image API endpoint: {url2}")

try:
    response = requests.get(url2)
    print(f"Status Code: {response.status_code}")
    print(f"Content-Type: {response.headers.get('Content-Type')}")

    if response.status_code == 200:
        data = response.json()
        print(f"Image data keys: {list(data.keys())}")
        print(f"Image width: {data.get('width')}")
        print(f"Image height: {data.get('height')}")
        print(f"Image data length: {len(data.get('image', ''))}")
        print("DICOM image API endpoint is working correctly")
    else:
        print(f"Error: {response.text}")

except Exception as e:
    print(f"Error: {e}")

# Also test if the study exists in database
print("\nTesting database connection...")
try:
    from app import app, db
    from models import DicomStudy

    with app.app_context():
        study = DicomStudy.query.filter_by(study_uid=study_uid).first()
        if study:
            print(f"Study found in database:")
            print(f"  - File path: {study.file_path}")
            print(f"  - Anonymized filename: {study.anonymized_filename}")
            print(f"  - Study description: {study.study_description}")

            file_exists = os.path.exists(study.file_path)
            print(f"  - File exists: {file_exists}")

            if not file_exists:
                # Try alternative path
                upload_folder = app.config['UPLOAD_FOLDER']
                alt_path = os.path.join(upload_folder, study.anonymized_filename)
                alt_exists = os.path.exists(alt_path)
                print(f"  - Alternative path exists: {alt_exists}")
                print(f"  - Alternative path: {alt_path}")

                # List files in upload folder
                if os.path.exists(upload_folder):
                    files = os.listdir(upload_folder)
                    print(f"  - Files in upload folder: {files}")
        else:
            print("Study not found in database")

except Exception as e:
    print(f"Database error: {e}")
