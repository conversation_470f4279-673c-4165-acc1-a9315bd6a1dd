#!/usr/bin/env python3
"""
Direct MySQL Migration script to add patient information fields
"""

import MySQLdb
import sys

def run_migration():
    """Add patient information columns directly to MySQL database"""
    
    try:
        # Connect to MySQL database
        # Update these credentials to match your MySQL setup
        connection = MySQLdb.connect(
            host='localhost',
            user='root',  # Update with your MySQL username
            passwd='',    # Update with your MySQL password
            db='medshare_dicom'  # Update with your database name
        )
        
        cursor = connection.cursor()
        
        print("Connected to MySQL database successfully!")
        print("Starting migration...")
        
        # SQL commands to add new columns
        migration_commands = [
            "ALTER TABLE dicom_studies ADD COLUMN patient_name VARCHAR(255)",
            "ALTER TABLE dicom_studies ADD COLUMN patient_birth_date VARCHAR(20)",
            "ALTER TABLE dicom_studies ADD COLUMN patient_sex VARCHAR(10)",
            "ALTER TABLE dicom_studies ADD COLUMN patient_weight VARCHAR(20)",
            "ALTER TABLE dicom_studies ADD COLUMN performing_physician VARCHAR(255)",
            "ALTER TABLE dicom_studies ADD COLUMN requesting_physician VARCHAR(255)",
            "ALTER TABLE dicom_studies ADD COLUMN study_time VARCHAR(20)",
            "ALTER TABLE dicom_studies ADD COLUMN accession_number VARCHAR(100)",
            "ALTER TABLE dicom_studies ADD COLUMN study_id_dicom VARCHAR(100)"
        ]
        
        for i, command in enumerate(migration_commands):
            try:
                print(f"[{i+1}/{len(migration_commands)}] Executing: {command}")
                cursor.execute(command)
                connection.commit()
                print("✓ Success")
            except MySQLdb.OperationalError as e:
                if "Duplicate column name" in str(e):
                    print(f"✓ Column already exists, skipping...")
                else:
                    print(f"✗ Error: {str(e)}")
                    continue
        
        cursor.close()
        connection.close()
        
        print("\nMySQL database migration completed successfully!")
        print("\nNow you can upload new DICOM files and they will display full patient information.")
        print("Note: Existing studies will show 'N/A' for patient information until re-uploaded.")
        
        return True
        
    except Exception as e:
        print(f"Migration failed: {str(e)}")
        print("Please check your MySQL connection settings in this script.")
        return False

if __name__ == '__main__':
    print("DICOM Patient Information Direct MySQL Migration")
    print("===============================================")
    print("This will add patient information fields to your MySQL database.")
    print("Make sure to update the MySQL connection settings in this script.")
    print()
    
    response = input("Do you want to proceed? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        if run_migration():
            print("\n✅ Migration completed successfully!")
        else:
            print("\n❌ Migration failed!")
            sys.exit(1)
    else:
        print("Migration cancelled.")
