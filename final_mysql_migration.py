#!/usr/bin/env python3
"""
Final MySQL Migration script to add ALL missing patient information fields
This script will check each column individually and add only the missing ones
"""

import sys
import MySQLdb
from sqlalchemy import create_engine, text
import os

def get_database_config():
    """Get database configuration from environment or use defaults"""
    # You may need to update these values to match your MySQL setup
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASSWORD', ''),
        'database': os.getenv('DB_NAME', 'medshare_dicom'),
        'port': int(os.getenv('DB_PORT', 3306))
    }
    return config

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in the table"""
    try:
        cursor.execute(f"SHOW COLUMNS FROM {table_name} LIKE '{column_name}'")
        result = cursor.fetchone()
        return result is not None
    except Exception as e:
        print(f"Error checking column {column_name}: {e}")
        return False

def run_final_migration():
    """Add all missing patient information columns to MySQL database"""
    
    config = get_database_config()
    
    try:
        # Connect to MySQL database
        print("Connecting to MySQL database...")
        connection = MySQLdb.connect(
            host=config['host'],
            user=config['user'],
            passwd=config['password'],
            db=config['database'],
            port=config['port']
        )
        
        cursor = connection.cursor()
        print("✓ Connected to MySQL database successfully!")
        
        # Define all columns that need to exist
        required_columns = {
            'patient_id': 'VARCHAR(100)',
            'patient_name': 'VARCHAR(255)',
            'patient_birth_date': 'VARCHAR(20)',
            'patient_sex': 'VARCHAR(10)',
            'patient_age': 'VARCHAR(10)',
            'patient_weight': 'VARCHAR(20)',
            'referring_physician': 'VARCHAR(255)',
            'performing_physician': 'VARCHAR(255)',
            'requesting_physician': 'VARCHAR(255)',
            'study_time': 'VARCHAR(20)',
            'accession_number': 'VARCHAR(100)',
            'study_id_dicom': 'VARCHAR(100)',
            'diagnosis': 'TEXT',
            'findings': 'TEXT',
            'impression': 'TEXT',
            'recommendations': 'TEXT',
            'report_date': 'DATETIME',
            'reporting_physician': 'VARCHAR(255)'
        }
        
        print(f"\nChecking {len(required_columns)} required columns...")
        
        missing_columns = []
        existing_columns = []
        
        # Check each column
        for column_name, column_type in required_columns.items():
            if check_column_exists(cursor, 'dicom_studies', column_name):
                existing_columns.append(column_name)
                print(f"✓ {column_name} - EXISTS")
            else:
                missing_columns.append((column_name, column_type))
                print(f"✗ {column_name} - MISSING")
        
        print(f"\nSummary:")
        print(f"✓ Existing columns: {len(existing_columns)}")
        print(f"✗ Missing columns: {len(missing_columns)}")
        
        if not missing_columns:
            print("\n🎉 All required columns already exist! No migration needed.")
            cursor.close()
            connection.close()
            return True
        
        print(f"\nAdding {len(missing_columns)} missing columns...")
        
        # Add missing columns
        for i, (column_name, column_type) in enumerate(missing_columns):
            try:
                sql = f"ALTER TABLE dicom_studies ADD COLUMN {column_name} {column_type}"
                print(f"[{i+1}/{len(missing_columns)}] Adding {column_name}...")
                cursor.execute(sql)
                connection.commit()
                print(f"✓ Successfully added {column_name}")
            except MySQLdb.OperationalError as e:
                if "Duplicate column name" in str(e):
                    print(f"✓ {column_name} already exists (duplicate)")
                else:
                    print(f"✗ Error adding {column_name}: {e}")
                    continue
            except Exception as e:
                print(f"✗ Unexpected error adding {column_name}: {e}")
                continue
        
        # Verify all columns were added
        print("\nVerifying migration...")
        final_missing = []
        for column_name, column_type in required_columns.items():
            if not check_column_exists(cursor, 'dicom_studies', column_name):
                final_missing.append(column_name)
        
        cursor.close()
        connection.close()
        
        if final_missing:
            print(f"\n❌ Migration incomplete. Still missing: {final_missing}")
            return False
        else:
            print("\n✅ Migration completed successfully!")
            print("All required patient information columns have been added.")
            print("\nYou can now restart your Flask application.")
            return True
        
    except MySQLdb.Error as e:
        print(f"❌ MySQL Error: {e}")
        print("\nPlease check your database connection settings:")
        print(f"Host: {config['host']}")
        print(f"User: {config['user']}")
        print(f"Database: {config['database']}")
        print(f"Port: {config['port']}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("DICOM Patient Information - Final MySQL Migration")
    print("=" * 60)
    print("This script will add all missing patient information columns")
    print("to your MySQL database for the DICOM viewer.")
    print()
    
    # Show current configuration
    config = get_database_config()
    print("Database Configuration:")
    print(f"  Host: {config['host']}")
    print(f"  User: {config['user']}")
    print(f"  Database: {config['database']}")
    print(f"  Port: {config['port']}")
    print()
    
    response = input("Do you want to proceed with the migration? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        if run_final_migration():
            print("\n🎉 Migration successful!")
            print("You can now restart your Flask application and the patient")
            print("information should display correctly.")
        else:
            print("\n💥 Migration failed!")
            print("Please check the error messages above and try again.")
            sys.exit(1)
    else:
        print("Migration cancelled.")
