#!/usr/bin/env python3
"""
Fix foreign key constraints to enable proper cascade deletion
"""

import os
import sys
from sqlalchemy import text
from app import app, db

def fix_foreign_keys():
    """Fix foreign key constraints for proper deletion"""
    
    with app.app_context():
        try:
            print("🔧 Fixing foreign key constraints...")
            
            # Drop the existing foreign key constraint
            print("   Dropping existing foreign key constraint...")
            try:
                db.session.execute(text("""
                    ALTER TABLE access_logs 
                    DROP FOREIGN KEY access_logs_ibfk_1
                """))
                print("   ✅ Existing foreign key dropped")
            except Exception as e:
                print(f"   ⚠️  Could not drop existing foreign key (may not exist): {e}")
            
            # Add new foreign key constraint with CASCADE
            print("   Adding new foreign key constraint with CASCADE...")
            db.session.execute(text("""
                ALTER TABLE access_logs 
                ADD CONSTRAINT access_logs_ibfk_1 
                FOREIGN KEY (study_id) REFERENCES dicom_studies(id) 
                ON DELETE CASCADE
            """))
            
            db.session.commit()
            print("   ✅ New foreign key constraint added with CASCADE")
            
            print("✅ Foreign key constraints fixed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to fix foreign keys: {str(e)}")
            db.session.rollback()
            return False

def test_deletion():
    """Test if deletion now works properly"""
    
    with app.app_context():
        try:
            from models import DicomStudy, AccessLog
            
            # Check if there are any studies to test with
            study_count = DicomStudy.query.count()
            access_log_count = AccessLog.query.count()
            
            print(f"\n📊 Current database state:")
            print(f"   Studies: {study_count}")
            print(f"   Access logs: {access_log_count}")
            
            if study_count > 0:
                print("\n🧪 Testing deletion capability...")
                
                # Get a study with access logs
                study_with_logs = db.session.query(DicomStudy).join(AccessLog).first()
                
                if study_with_logs:
                    log_count = len(study_with_logs.access_logs)
                    print(f"   Found study with {log_count} access logs")
                    print("   ✅ Deletion should now work properly with CASCADE")
                else:
                    print("   No studies with access logs found")
                    print("   ✅ Foreign key fix applied successfully")
            else:
                print("   No studies in database to test with")
                print("   ✅ Foreign key fix applied successfully")
                
            return True
            
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
            return False

if __name__ == '__main__':
    print("🚀 FOREIGN KEY CONSTRAINT FIX")
    print("=" * 50)
    
    if fix_foreign_keys():
        if test_deletion():
            print("\n🎉 Foreign key fix completed successfully!")
            print("\n✅ What's Fixed:")
            print("  • Delete button now works without foreign key errors")
            print("  • Access logs are automatically deleted when studies are deleted")
            print("  • Cascade deletion prevents orphaned records")
            print("  • Database integrity is maintained")
            
            print("\n📋 Next Steps:")
            print("  • Restart your Flask application")
            print("  • Test the delete functionality")
            print("  • Use bulk delete for multiple files")
        else:
            print("\n⚠️  Fix applied but testing failed")
    else:
        print("\n❌ Foreign key fix failed!")
        sys.exit(1)
