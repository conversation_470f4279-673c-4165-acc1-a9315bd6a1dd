#!/usr/bin/env python3
"""
Simple MySQL Migration script to add patient information fields
Run this script to update the MySQL database schema for displaying full patient information
"""

import os
import sys
from app import app, db
from sqlalchemy import text

def fix_mysql_database():
    """Add patient information columns to existing MySQL database"""

    with app.app_context():
        try:
            print("Starting MySQL database migration...")

            # SQL commands to add new columns
            migration_commands = [
                "ALTER TABLE dicom_studies ADD COLUMN patient_name VARCHAR(255)",
                "ALTER TABLE dicom_studies ADD COLUMN patient_birth_date VARCHAR(20)",
                "ALTER TABLE dicom_studies ADD COLUMN patient_sex VARCHAR(10)",
                "ALTER TABLE dicom_studies ADD COLUMN patient_weight VARCHAR(20)",
                "ALTER TABLE dicom_studies ADD COLUMN performing_physician VARCHAR(255)",
                "ALTER TABLE dicom_studies ADD COLUMN requesting_physician VARCHAR(255)",
                "ALTER TABLE dicom_studies ADD COLUMN study_time VARCHAR(20)",
                "ALTER TABLE dicom_studies ADD COLUMN accession_number VARCHAR(100)",
                "ALTER TABLE dicom_studies ADD COLUMN study_id_dicom VARCHAR(100)"
            ]

            for i, command in enumerate(migration_commands):
                try:
                    print(f"[{i+1}/{len(migration_commands)}] Executing: {command}")
                    with db.engine.connect() as connection:
                        connection.execute(text(command))
                        connection.commit()
                    print("✓ Success")
                except Exception as e:
                    if "Duplicate column name" in str(e):
                        print(f"✓ Column already exists, skipping...")
                    else:
                        print(f"✗ Error: {str(e)}")
                        continue

            print("\nMySQL database migration completed successfully!")
            print("\nNow you can upload new DICOM files and they will display full patient information.")
            print("Note: Existing studies will show 'N/A' for patient information until re-uploaded.")

        except Exception as e:
            print(f"Migration failed: {str(e)}")
            return False

    return True

if __name__ == '__main__':
    print("DICOM Patient Information MySQL Migration")
    print("=========================================")
    print("This will add patient information fields to your MySQL database.")
    print("This allows displaying full patient data for medical collaboration.")
    print()

    if fix_mysql_database():
        print("\n✅ Migration completed successfully!")
    else:
        print("\n❌ Migration failed!")
        sys.exit(1)
