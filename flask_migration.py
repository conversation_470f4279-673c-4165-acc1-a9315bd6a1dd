#!/usr/bin/env python3
"""
Flask-based migration script using the same database connection as the app
This will add all missing patient information columns to the shaafi_scan database
"""

import sys
from app import app, db
from sqlalchemy import text

def check_column_exists(column_name):
    """Check if a column exists in the dicom_studies table"""
    try:
        with db.engine.connect() as connection:
            result = connection.execute(text(f"SHOW COLUMNS FROM dicom_studies LIKE '{column_name}'"))
            return result.fetchone() is not None
    except Exception as e:
        print(f"Error checking column {column_name}: {e}")
        return False

def add_column(column_name, column_type):
    """Add a column to the dicom_studies table"""
    try:
        sql = f"ALTER TABLE dicom_studies ADD COLUMN {column_name} {column_type}"
        with db.engine.connect() as connection:
            connection.execute(text(sql))
            connection.commit()
        return True
    except Exception as e:
        if "Duplicate column name" in str(e):
            return True  # Column already exists
        else:
            print(f"Error adding column {column_name}: {e}")
            return False

def run_flask_migration():
    """Add all missing patient information columns using Flask's database connection"""
    
    with app.app_context():
        print("Using Flask app database connection...")
        print(f"Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
        
        # Define all columns that need to exist
        required_columns = {
            'patient_id': 'VARCHAR(100)',
            'patient_name': 'VARCHAR(255)',
            'patient_birth_date': 'VARCHAR(20)',
            'patient_sex': 'VARCHAR(10)',
            'patient_age': 'VARCHAR(10)',
            'patient_weight': 'VARCHAR(20)',
            'referring_physician': 'VARCHAR(255)',
            'performing_physician': 'VARCHAR(255)',
            'requesting_physician': 'VARCHAR(255)',
            'study_time': 'VARCHAR(20)',
            'accession_number': 'VARCHAR(100)',
            'study_id_dicom': 'VARCHAR(100)',
            'diagnosis': 'TEXT',
            'findings': 'TEXT',
            'impression': 'TEXT',
            'recommendations': 'TEXT',
            'report_date': 'DATETIME',
            'reporting_physician': 'VARCHAR(255)'
        }
        
        print(f"\nChecking {len(required_columns)} required columns...")
        
        missing_columns = []
        existing_columns = []
        
        # Check each column
        for column_name, column_type in required_columns.items():
            if check_column_exists(column_name):
                existing_columns.append(column_name)
                print(f"✓ {column_name} - EXISTS")
            else:
                missing_columns.append((column_name, column_type))
                print(f"✗ {column_name} - MISSING")
        
        print(f"\nSummary:")
        print(f"✓ Existing columns: {len(existing_columns)}")
        print(f"✗ Missing columns: {len(missing_columns)}")
        
        if not missing_columns:
            print("\n🎉 All required columns already exist! No migration needed.")
            return True
        
        print(f"\nAdding {len(missing_columns)} missing columns...")
        
        # Add missing columns
        success_count = 0
        for i, (column_name, column_type) in enumerate(missing_columns):
            print(f"[{i+1}/{len(missing_columns)}] Adding {column_name}...")
            if add_column(column_name, column_type):
                print(f"✓ Successfully added {column_name}")
                success_count += 1
            else:
                print(f"✗ Failed to add {column_name}")
        
        # Verify all columns were added
        print("\nVerifying migration...")
        final_missing = []
        for column_name, column_type in required_columns.items():
            if not check_column_exists(column_name):
                final_missing.append(column_name)
        
        if final_missing:
            print(f"\n❌ Migration incomplete. Still missing: {final_missing}")
            return False
        else:
            print(f"\n✅ Migration completed successfully!")
            print(f"Added {success_count} columns to the shaafi_scan database.")
            print("\nAll required patient information columns are now available.")
            print("You can now restart your Flask application.")
            return True

if __name__ == '__main__':
    print("=" * 60)
    print("DICOM Patient Information - Flask Migration")
    print("=" * 60)
    print("This script will add all missing patient information columns")
    print("to your shaafi_scan database using Flask's database connection.")
    print()
    
    try:
        if run_flask_migration():
            print("\n🎉 Migration successful!")
            print("Your DICOM viewer should now display full patient information.")
        else:
            print("\n💥 Migration failed!")
            print("Please check the error messages above.")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print("Please make sure your Flask app configuration is correct.")
        sys.exit(1)
