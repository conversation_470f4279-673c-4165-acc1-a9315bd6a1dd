#!/usr/bin/env python3
"""
Database migration script to add series support for multi-file DICOM uploads
"""

import os
import sys
from sqlalchemy import text
from app import app, db

def migrate_database():
    """Add series fields to DicomStudy table"""

    with app.app_context():
        try:
            # Check if columns already exist
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('dicom_studies')]

            migrations_needed = []

            if 'series_uid' not in columns:
                migrations_needed.append("ALTER TABLE dicom_studies ADD COLUMN series_uid VARCHAR(128)")

            if 'series_number' not in columns:
                migrations_needed.append("ALTER TABLE dicom_studies ADD COLUMN series_number INTEGER DEFAULT 1")

            if 'total_series_files' not in columns:
                migrations_needed.append("ALTER TABLE dicom_studies ADD COLUMN total_series_files INTEGER DEFAULT 1")

            if migrations_needed:
                print("🔄 Running database migrations...")

                for migration in migrations_needed:
                    print(f"   Executing: {migration}")
                    db.session.execute(text(migration))

                db.session.commit()
                print("✅ Database migration completed successfully!")

                # Update existing records to have default values
                print("🔄 Updating existing records...")
                db.session.execute(text("""
                    UPDATE dicom_studies
                    SET series_number = 1, total_series_files = 1
                    WHERE series_number IS NULL OR total_series_files IS NULL
                """))
                db.session.commit()
                print("✅ Existing records updated!")

            else:
                print("✅ Database is already up to date!")

        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            db.session.rollback()
            return False

    return True

if __name__ == '__main__':
    print("🚀 DICOM Database Migration")
    print("=" * 50)

    if migrate_database():
        print("\n🎉 Migration completed successfully!")
        print("Your DICOM platform now supports:")
        print("• Multiple DICOM files from ZIP/RAR archives")
        print("• Series grouping and gallery viewing")
        print("• Side-by-side image comparison")
    else:
        print("\n❌ Migration failed!")
        sys.exit(1)
