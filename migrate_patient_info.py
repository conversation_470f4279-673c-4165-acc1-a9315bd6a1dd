#!/usr/bin/env python3
"""
Migration script to add patient information fields to existing database
Run this script to update the database schema for displaying full patient information
"""

import os
import sys
from app import app, db
from models import DicomStudy

def migrate_database():
    """Add patient information columns to existing database"""
    
    with app.app_context():
        try:
            print("Starting database migration...")
            
            # Check if we're using SQLite (which is likely for this project)
            if 'sqlite' in str(db.engine.url):
                print("Detected SQLite database")
                
                # For SQLite, we need to add columns one by one
                columns_to_add = [
                    ('patient_name', 'VARCHAR(255)'),
                    ('patient_birth_date', 'VARCHAR(20)'),
                    ('patient_sex', 'VARCHAR(10)'),
                    ('patient_weight', 'VARCHAR(20)'),
                    ('performing_physician', 'VARCHAR(255)'),
                    ('requesting_physician', 'VARCHAR(255)'),
                    ('study_time', 'VARCHAR(20)'),
                    ('accession_number', 'VARCHAR(100)'),
                    ('study_id_dicom', 'VARCHAR(100)')
                ]
                
                for column_name, column_type in columns_to_add:
                    try:
                        # Check if column already exists
                        result = db.engine.execute(f"PRAGMA table_info(dicom_studies)")
                        existing_columns = [row[1] for row in result]
                        
                        if column_name not in existing_columns:
                            print(f"Adding column: {column_name}")
                            db.engine.execute(f"ALTER TABLE dicom_studies ADD COLUMN {column_name} {column_type}")
                        else:
                            print(f"Column {column_name} already exists, skipping...")
                            
                    except Exception as e:
                        print(f"Error adding column {column_name}: {str(e)}")
                        continue
                
            else:
                # For other databases, use SQLAlchemy's create_all
                print("Using SQLAlchemy create_all for non-SQLite database")
                db.create_all()
            
            print("Database migration completed successfully!")
            print("\nNow you can upload new DICOM files and they will display full patient information.")
            print("Note: Existing studies will show 'N/A' for patient information until re-uploaded.")
            
        except Exception as e:
            print(f"Migration failed: {str(e)}")
            return False
            
    return True

if __name__ == '__main__':
    print("DICOM Patient Information Migration")
    print("===================================")
    print("This will add patient information fields to your database.")
    print("This allows displaying full patient data for medical collaboration.")
    print()
    
    response = input("Do you want to proceed? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        if migrate_database():
            print("\n✅ Migration completed successfully!")
        else:
            print("\n❌ Migration failed!")
            sys.exit(1)
    else:
        print("Migration cancelled.")
