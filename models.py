from app import db
from datetime import datetime
import uuid
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

class DicomStudy(db.Model):
    __tablename__ = 'dicom_studies'

    id = db.Column(db.Integer, primary_key=True)
    study_uid = db.Column(db.String(128), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    original_filename = db.Column(db.String(255), nullable=False)
    anonymized_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)

    # DICOM metadata (anonymized)
    study_date = db.Column(db.String(20))
    study_description = db.Column(db.String(255))
    modality = db.Column(db.String(20))
    institution_name = db.Column(db.String(255))
    manufacturer = db.Column(db.String(255))

    # Sharing settings
    is_shared = db.Column(db.Boolean, default=False)
    share_token = db.Column(db.String(64), unique=True)
    share_expires = db.Column(db.DateTime)

    # Access tracking
    view_count = db.Column(db.Integer, default=0)
    last_viewed = db.Column(db.DateTime)

    # Patient information (for medical collaboration)
    patient_name = db.Column(db.String(255))
    patient_id = db.Column(db.String(100))
    patient_birth_date = db.Column(db.String(20))
    patient_sex = db.Column(db.String(10))
    patient_age = db.Column(db.String(10))
    patient_weight = db.Column(db.String(20))
    referring_physician = db.Column(db.String(255))
    performing_physician = db.Column(db.String(255))
    requesting_physician = db.Column(db.String(255))
    study_time = db.Column(db.String(20))
    accession_number = db.Column(db.String(100))
    study_id_dicom = db.Column(db.String(100))

    # Doctor's report and notes
    diagnosis = db.Column(db.Text)
    findings = db.Column(db.Text)
    impression = db.Column(db.Text)
    recommendations = db.Column(db.Text)
    report_date = db.Column(db.DateTime)
    reporting_physician = db.Column(db.String(255))

    def __repr__(self):
        return f'<DicomStudy {self.study_uid}>'

    def generate_share_token(self):
        """Generate a unique share token for this study"""
        self.share_token = str(uuid.uuid4())
        return self.share_token

class AccessLog(db.Model):
    __tablename__ = 'access_logs'

    id = db.Column(db.Integer, primary_key=True)
    study_id = db.Column(db.Integer, db.ForeignKey('dicom_studies.id'), nullable=False)
    access_time = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))

    study = db.relationship('DicomStudy', backref=db.backref('access_logs', lazy=True))

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    user_type = db.Column(db.String(20), nullable=False, default='user')  # 'admin' or 'user'
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def __repr__(self):
        return f'<User {self.username}>'

    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        """Check if user is admin"""
        return self.user_type == 'admin'

    def get_id(self):
        """Return user id as string for Flask-Login"""
        return str(self.id)
