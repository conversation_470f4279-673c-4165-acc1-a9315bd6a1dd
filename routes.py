import os
import logging
from flask import render_template, request, redirect, url_for, flash, jsonify, send_file, abort, current_app
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
from utils.dicom_processor import DicomProcessor
from utils.anonymizer import DicomAnonymizer
from utils.qr_generator import generate_qr_code

# Import app and db from the app module
from app import app, db
from models import DicomStudy, AccessLog

ALLOWED_EXTENSIONS = {'dcm', 'dicom', 'zip'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Home page with recent studies and statistics"""
    recent_studies = DicomStudy.query.order_by(DicomStudy.upload_date.desc()).limit(10).all()
    total_studies = DicomStudy.query.count()
    total_shared = DicomStudy.query.filter_by(is_shared=True).count()

    return render_template('index.html',
                         recent_studies=recent_studies,
                         total_studies=total_studies,
                         total_shared=total_shared)

@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    """Handle DICOM file upload and processing"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected', 'error')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected', 'error')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                # Ensure upload folder exists
                if not os.path.exists(app.config['UPLOAD_FOLDER']):
                    os.makedirs(app.config['UPLOAD_FOLDER'])

                # Create secure filename with timestamp
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{timestamp}_{filename}"

                # Get absolute paths
                upload_folder = os.path.abspath(app.config['UPLOAD_FOLDER'])
                filepath = os.path.join(upload_folder, filename)

                app.logger.info(f'Saving file to: {filepath}')
                file.save(filepath)
                app.logger.info(f'File saved successfully. Exists: {os.path.exists(filepath)}')

                # Process DICOM file
                processor = DicomProcessor()
                if not processor.validate_dicom(filepath):
                    os.remove(filepath)
                    flash('Invalid DICOM file', 'error')
                    return redirect(request.url)

                # Extract metadata from original file (including patient information)
                metadata = processor.extract_metadata(filepath)

                # Anonymize DICOM for processing (but keep original data in database)
                anonymizer = DicomAnonymizer()
                anonymized_path = anonymizer.anonymize_file(filepath)
                anonymized_path = os.path.abspath(anonymized_path)
                app.logger.info(f'File anonymized. New path: {anonymized_path}')
                app.logger.info(f'Anonymized file exists: {os.path.exists(anonymized_path)}')

                # Create database record with full patient information for medical collaboration
                study = DicomStudy(
                    original_filename=file.filename,
                    anonymized_filename=os.path.basename(anonymized_path),
                    file_path=anonymized_path,
                    file_size=os.path.getsize(anonymized_path),
                    study_date=metadata.get('StudyDate'),
                    study_description=metadata.get('StudyDescription'),
                    modality=metadata.get('Modality'),
                    institution_name=metadata.get('InstitutionName'),
                    manufacturer=metadata.get('Manufacturer'),
                    # Patient information for medical collaboration
                    patient_name=metadata.get('PatientName'),
                    patient_id=metadata.get('PatientID'),
                    patient_birth_date=metadata.get('PatientBirthDate'),
                    patient_sex=metadata.get('PatientSex'),
                    patient_age=metadata.get('PatientAge'),
                    patient_weight=metadata.get('PatientWeight'),
                    referring_physician=metadata.get('ReferringPhysicianName'),
                    performing_physician=metadata.get('PerformingPhysicianName'),
                    requesting_physician=metadata.get('RequestingPhysician'),
                    study_time=metadata.get('StudyTime'),
                    accession_number=metadata.get('AccessionNumber'),
                    study_id_dicom=metadata.get('StudyID')
                )

                db.session.add(study)
                db.session.commit()

                # Remove original file, keep anonymized version
                if os.path.exists(filepath) and filepath != anonymized_path:
                    os.remove(filepath)

                flash(f'DICOM file uploaded and anonymized successfully! Study ID: {study.study_uid}', 'success')
                return redirect(url_for('viewer', study_uid=study.study_uid))

            except Exception as e:
                logging.error(f"Error processing file: {str(e)}")
                flash(f'Error processing file: {str(e)}', 'error')
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload DICOM (.dcm, .dicom) or ZIP files.', 'error')

    return render_template('upload.html')

@app.route('/viewer/<study_uid>')
def viewer(study_uid):
    """DICOM viewer page"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    # Update view count and last viewed
    study.view_count += 1
    study.last_viewed = datetime.utcnow()

    # Log access
    access_log = AccessLog(
        study_id=study.id,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent', '')
    )
    db.session.add(access_log)
    db.session.commit()

    return render_template('viewer.html', study=study, now=datetime.utcnow())

@app.route('/share/<study_uid>', methods=['GET', 'POST'])
def share_study(study_uid):
    """Generate shareable link and QR code for a study"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    if request.method == 'POST':
        # Generate share token and set expiration
        study.generate_share_token()
        study.is_shared = True

        # Set expiration (default 30 days)
        expiry_days = int(request.form.get('expiry_days', 30))
        study.share_expires = datetime.utcnow() + timedelta(days=expiry_days)

        db.session.commit()

        # Generate QR code
        share_url = url_for('shared_viewer', token=study.share_token, _external=True)
        qr_path = generate_qr_code(share_url, study.study_uid)

        flash('Shareable link generated successfully!', 'success')
        return render_template('share.html', study=study, share_url=share_url, qr_path=qr_path, now=datetime.utcnow())

    return render_template('share.html', study=study, now=datetime.utcnow())

@app.route('/shared/<token>')
def shared_viewer(token):
    """Public viewer for shared studies"""
    study = DicomStudy.query.filter_by(share_token=token).first_or_404()

    # Check if share has expired
    if study.share_expires and study.share_expires < datetime.utcnow():
        abort(410)  # Gone

    if not study.is_shared:
        abort(404)

    # Update view count and last viewed
    study.view_count += 1
    study.last_viewed = datetime.utcnow()

    # Log access
    access_log = AccessLog(
        study_id=study.id,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent', '')
    )
    db.session.add(access_log)
    db.session.commit()

    return render_template('viewer.html', study=study, is_shared=True, now=datetime.utcnow())

@app.route('/admin')
def admin():
    """Admin dashboard for file management"""
    studies = DicomStudy.query.order_by(DicomStudy.upload_date.desc()).all()
    return render_template('admin.html', studies=studies)

@app.route('/update-report/<study_uid>', methods=['POST'])
def update_report(study_uid):
    """Update patient information and doctor's report"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    try:
        # Update patient information
        study.patient_age = request.form.get('patient_age', '').strip()
        study.patient_gender = request.form.get('patient_gender', '').strip()
        study.referring_physician = request.form.get('referring_physician', '').strip()

        # Update doctor's report
        study.diagnosis = request.form.get('diagnosis', '').strip()
        study.findings = request.form.get('findings', '').strip()
        study.impression = request.form.get('impression', '').strip()
        study.recommendations = request.form.get('recommendations', '').strip()
        study.reporting_physician = request.form.get('reporting_physician', '').strip()

        # Update report date if any report fields were filled
        if any([study.diagnosis, study.findings, study.impression, study.recommendations]):
            study.report_date = datetime.utcnow()

        db.session.commit()
        flash('Patient information and report updated successfully!', 'success')

    except Exception as e:
        logging.error(f"Error updating report: {str(e)}")
        flash(f'Error updating report: {str(e)}', 'error')

    return redirect(url_for('viewer', study_uid=study_uid))

@app.route('/delete/<study_uid>', methods=['POST'])
def delete_study(study_uid):
    """Delete a study and its associated file"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    try:
        # Delete file from filesystem
        if os.path.exists(study.file_path):
            os.remove(study.file_path)

        # Delete from database
        db.session.delete(study)
        db.session.commit()

        flash('Study deleted successfully!', 'success')
    except Exception as e:
        logging.error(f"Error deleting study: {str(e)}")
        flash(f'Error deleting study: {str(e)}', 'error')

    return redirect(url_for('admin'))

@app.route('/download/<study_uid>')
def download_study(study_uid):
    """Download DICOM file"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    if not os.path.exists(study.file_path):
        flash('File not found', 'error')
        return redirect(url_for('admin'))

    return send_file(study.file_path, as_attachment=True,
                    download_name=study.anonymized_filename)

@app.route('/api/dicom-data/<study_uid>', methods=['GET', 'OPTIONS'])
def get_dicom_data(study_uid):
    """API endpoint to serve DICOM data"""
    app.logger.info(f'Received request for DICOM data with study_uid: {study_uid}')

    if request.method == 'OPTIONS':
        response = app.make_default_options_response()
    else:
        app.logger.info(f'Looking up study in database...')
        study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()
        app.logger.info(f'Found study in database with file_path: {study.file_path}')

        try:
            # Handle both absolute and relative paths
            if os.path.isabs(study.file_path):
                file_path = study.file_path
            else:
                # If relative path, join with upload folder
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], study.file_path)

            # Normalize the path
            file_path = os.path.normpath(file_path)
            app.logger.info(f'Normalized file path: {file_path}')
            app.logger.info(f'Current working directory: {os.getcwd()}')
            app.logger.info(f'File exists check: {os.path.exists(file_path)}')
            app.logger.info(f'Upload folder path: {app.config["UPLOAD_FOLDER"]}')
            app.logger.info(f'Upload folder exists: {os.path.exists(app.config["UPLOAD_FOLDER"])}')

            # List files in upload folder for debugging
            if os.path.exists(app.config['UPLOAD_FOLDER']):
                files_in_upload = os.listdir(app.config['UPLOAD_FOLDER'])
                app.logger.info(f'Files in upload folder: {files_in_upload}')

                # Try to find the file by filename if exact path doesn't work
                if not os.path.exists(file_path):
                    filename = os.path.basename(study.file_path)
                    alternative_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    app.logger.info(f'Trying alternative path: {alternative_path}')
                    if os.path.exists(alternative_path):
                        file_path = alternative_path
                        app.logger.info(f'Found file at alternative path: {file_path}')

            if not os.path.exists(file_path):
                app.logger.error(f'DICOM file not found: {file_path}')
                app.logger.error(f'Study file_path from DB: {study.file_path}')
                app.logger.error(f'Anonymized filename: {study.anonymized_filename}')
                abort(404)

            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Create response with file content
            response = app.response_class(
                file_content,
                mimetype='application/dicom'
            )

            # Set content length
            response.headers['Content-Length'] = len(file_content)

        except IOError as e:
            app.logger.error(f'IO Error reading DICOM file: {str(e)}')
            abort(500)
        except Exception as e:
            app.logger.error(f'Error serving DICOM file: {str(e)}')
            abort(500)

    # Add CORS headers
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = '*'
    response.headers['Cache-Control'] = 'no-cache'

    return response

@app.route('/test-viewer')
def test_viewer():
    """Test viewer page for debugging"""
    return send_file('test_viewer.html')

# Trigger reload

@app.route('/api/dicom-image/<study_uid>')
def get_dicom_image(study_uid):
    """API endpoint to get DICOM image as base64-encoded image for simple viewer"""
    app.logger.info(f'Received request for DICOM image with study_uid: {study_uid}')

    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    try:
        # Handle both absolute and relative paths
        if os.path.isabs(study.file_path):
            file_path = study.file_path
        else:
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], study.file_path)

        file_path = os.path.normpath(file_path)

        # Try alternative path if file not found
        if not os.path.exists(file_path):
            filename = os.path.basename(study.file_path)
            alternative_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            if os.path.exists(alternative_path):
                file_path = alternative_path

        if not os.path.exists(file_path):
            app.logger.error(f'DICOM file not found: {file_path}')
            abort(404)

        # Use DICOM processor to extract image data
        from utils.dicom_processor import DicomProcessor
        processor = DicomProcessor()
        image_data = processor.get_image_data(file_path)

        return jsonify(image_data)

    except Exception as e:
        app.logger.error(f'Error processing DICOM image: {str(e)}')
        abort(500)

@app.route('/api/studies/<study_uid>/metadata')
def get_study_metadata(study_uid):
    """API endpoint to get study metadata in OHIF DICOMweb format"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    try:
        metadata = {
            '00100020': { 'Value': [study.study_uid], 'vr': 'LO' },  # Patient ID
            '0020000D': { 'Value': [study.study_uid], 'vr': 'UI' },  # Study Instance UID
            '00080020': { 'Value': [study.study_date], 'vr': 'DA' },  # Study Date
            '00081030': { 'Value': [study.study_description], 'vr': 'LO' },  # Study Description
            '00080060': { 'Value': [study.modality], 'vr': 'CS' },  # Modality
            '00080080': { 'Value': [study.institution_name], 'vr': 'LO' },  # Institution Name
        }

        return jsonify(metadata)

    except Exception as e:
        app.logger.error(f'Error getting study metadata: {str(e)}')
        abort(500)

@app.errorhandler(404)
def not_found_error(error):
    return render_template('base.html', error_message="Page not found"), 404

@app.errorhandler(410)
def gone_error(error):
    return render_template('base.html', error_message="This shared link has expired"), 410

@app.errorhandler(413)
def too_large_error(error):
    return render_template('base.html', error_message="File too large. Maximum size is 500MB"), 413
