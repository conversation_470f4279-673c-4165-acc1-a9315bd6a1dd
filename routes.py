import os
import logging
import uuid
from flask import render_template, request, redirect, url_for, flash, jsonify, send_file, abort, current_app, session
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
from flask_login import login_user, logout_user, login_required, current_user
from utils.dicom_processor import DicomProcessor
from utils.anonymizer import DicomAnonymizer
from utils.qr_generator import generate_qr_code
from utils.multi_file_processor import MultiFileProcessor

# Import app and db from the app module
from app import app, db
from models import DicomStudy, AccessLog, User
from forms import LoginForm, RegistrationForm, UserEditForm, ChangePasswordForm

ALLOWED_EXTENSIONS = {'dcm', 'dicom', 'dic', 'zip', 'rar'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Authentication Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user, remember=form.remember_me.data)
            user.last_login = datetime.utcnow()
            db.session.commit()

            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('index')
            flash(f'Welcome back, {user.name}!', 'success')
            return redirect(next_page)
        else:
            flash('Invalid username or password', 'error')

    return render_template('auth/login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('login'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    """User registration - only accessible by admins"""
    if current_user.is_authenticated and not current_user.is_admin():
        flash('Access denied. Only administrators can register new users.', 'error')
        return redirect(url_for('index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            name=form.name.data,
            username=form.username.data,
            email=form.email.data,
            user_type=form.user_type.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()

        flash(f'User {user.username} has been registered successfully!', 'success')
        if current_user.is_authenticated:
            return redirect(url_for('admin_users'))
        else:
            return redirect(url_for('login'))

    return render_template('auth/register.html', form=form)

@app.route('/')
@login_required
def index():
    """Home page with recent studies and statistics"""
    recent_studies = DicomStudy.query.order_by(DicomStudy.upload_date.desc()).limit(10).all()
    total_studies = DicomStudy.query.count()
    total_shared = DicomStudy.query.filter_by(is_shared=True).count()

    return render_template('index.html',
                         recent_studies=recent_studies,
                         total_studies=total_studies,
                         total_shared=total_shared)

def process_single_dicom(original_filename, filepath, dicom_processor, anonymizer, series_uid=None, series_number=1, total_series_files=1):
    """Process a single DICOM file"""
    try:
        # Validate DICOM file
        if not dicom_processor.validate_dicom(filepath):
            os.remove(filepath)
            app.logger.warning(f'Invalid DICOM file: {original_filename}')
            return None

        # Extract metadata from original file
        metadata = dicom_processor.extract_metadata(filepath)

        # Anonymize DICOM for processing
        anonymized_path = anonymizer.anonymize_file(filepath)
        anonymized_path = os.path.abspath(anonymized_path)

        app.logger.info(f'File anonymized: {anonymized_path}')

        # Create database record
        study = DicomStudy(
            original_filename=original_filename,
            anonymized_filename=os.path.basename(anonymized_path),
            file_path=anonymized_path,
            file_size=os.path.getsize(anonymized_path),
            study_date=metadata.get('StudyDate'),
            study_description=metadata.get('StudyDescription'),
            modality=metadata.get('Modality'),
            institution_name=metadata.get('InstitutionName'),
            manufacturer=metadata.get('Manufacturer'),
            # Patient information for medical collaboration
            patient_name=metadata.get('PatientName'),
            patient_id=metadata.get('PatientID'),
            patient_birth_date=metadata.get('PatientBirthDate'),
            patient_sex=metadata.get('PatientSex'),
            patient_age=metadata.get('PatientAge'),
            patient_weight=metadata.get('PatientWeight'),
            referring_physician=metadata.get('ReferringPhysicianName'),
            performing_physician=metadata.get('PerformingPhysicianName'),
            requesting_physician=metadata.get('RequestingPhysician'),
            study_time=metadata.get('StudyTime'),
            accession_number=metadata.get('AccessionNumber'),
            study_id_dicom=metadata.get('StudyID'),
            # Series information
            series_uid=series_uid,
            series_number=series_number,
            total_series_files=total_series_files
        )

        db.session.add(study)
        db.session.commit()

        # Remove original file, keep anonymized version
        if os.path.exists(filepath) and filepath != anonymized_path:
            os.remove(filepath)

        app.logger.info(f'Successfully processed DICOM: {original_filename}')
        return study

    except Exception as e:
        app.logger.error(f'Error processing DICOM {original_filename}: {str(e)}')
        if os.path.exists(filepath):
            os.remove(filepath)
        return None

def process_archive_file(original_filename, filepath, multi_processor, dicom_processor, anonymizer, temp_dirs):
    """Process archive file (ZIP or RAR) and extract DICOM files"""
    studies = []

    try:
        # Create temporary extraction directory
        import tempfile
        temp_dir = tempfile.mkdtemp(prefix='dicom_extract_')
        temp_dirs.append(temp_dir)

        app.logger.info(f'Extracting archive: {original_filename} to {temp_dir}')

        # Extract archive
        extracted_files = multi_processor.extract_archive(filepath, temp_dir)

        if not extracted_files:
            app.logger.warning(f'No DICOM files found in archive: {original_filename}')
            return studies

        app.logger.info(f'Found {len(extracted_files)} potential DICOM files in {original_filename}')

        # Generate series UID for this archive
        series_uid = str(uuid.uuid4())
        total_files = len(extracted_files)

        # Process each extracted file
        for index, extracted_file in enumerate(extracted_files, 1):
            try:
                # Get relative filename for database
                relative_name = os.path.relpath(extracted_file, temp_dir)
                archive_filename = f"{original_filename}:{relative_name}"

                # Process as DICOM with series information
                study = process_single_dicom(archive_filename, extracted_file, dicom_processor, anonymizer,
                                           series_uid=series_uid, series_number=index, total_series_files=total_files)
                if study:
                    studies.append(study)
                    app.logger.info(f'Successfully processed DICOM from archive: {relative_name} ({index}/{total_files})')
                else:
                    app.logger.warning(f'Failed to process file from archive: {relative_name}')

            except Exception as e:
                app.logger.error(f'Error processing extracted file {extracted_file}: {str(e)}')
                continue

        app.logger.info(f'Successfully processed {len(studies)} DICOM files from archive: {original_filename}')
        return studies

    except Exception as e:
        app.logger.error(f'Error processing archive {original_filename}: {str(e)}')
        return studies

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload_file():
    """Handle multiple DICOM files, ZIP, and RAR archive upload and processing"""
    if request.method == 'POST':
        # Check if files were uploaded
        if 'files' not in request.files:
            flash('No files selected', 'error')
            return redirect(request.url)

        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            flash('No files selected', 'error')
            return redirect(request.url)

        # Ensure upload folder exists
        if not os.path.exists(app.config['UPLOAD_FOLDER']):
            os.makedirs(app.config['UPLOAD_FOLDER'])

        # Initialize processors
        multi_processor = MultiFileProcessor(app.config['UPLOAD_FOLDER'])
        dicom_processor = DicomProcessor()
        anonymizer = DicomAnonymizer()

        processed_studies = []
        temp_dirs = []

        try:
            for file in files:
                if not file or file.filename == '':
                    continue

                if not allowed_file(file.filename):
                    flash(f'Unsupported file type: {file.filename}', 'warning')
                    continue

                app.logger.info(f'Processing file: {file.filename}')

                # Create secure filename with timestamp
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
                filename = f"{timestamp}_{filename}"

                # Save uploaded file
                upload_folder = os.path.abspath(app.config['UPLOAD_FOLDER'])
                filepath = os.path.join(upload_folder, filename)
                file.save(filepath)

                try:
                    if multi_processor.is_dicom_file(file.filename):
                        # Process single DICOM file
                        study = process_single_dicom(file.filename, filepath, dicom_processor, anonymizer)
                        if study:
                            processed_studies.append(study)

                    elif multi_processor.is_archive_file(file.filename):
                        # Process archive file
                        studies = process_archive_file(file.filename, filepath, multi_processor, dicom_processor, anonymizer, temp_dirs)
                        processed_studies.extend(studies)

                except Exception as e:
                    app.logger.error(f"Error processing {file.filename}: {str(e)}")
                    flash(f'Error processing {file.filename}: {str(e)}', 'error')
                    continue
                finally:
                    # Clean up uploaded file if it's not a DICOM (archives are cleaned up after extraction)
                    if os.path.exists(filepath) and not multi_processor.is_dicom_file(file.filename):
                        os.remove(filepath)

            # Clean up temporary directories
            multi_processor.cleanup_temp_files(temp_dirs)

            # Show results
            if processed_studies:
                if len(processed_studies) == 1:
                    flash(f'Successfully processed 1 DICOM file!', 'success')
                    return redirect(url_for('viewer', study_uid=processed_studies[0].study_uid))
                else:
                    # Check if studies are part of a series (from archive)
                    series_studies = [s for s in processed_studies if s.series_uid]
                    if series_studies:
                        # Redirect to gallery view for series
                        series_uid = series_studies[0].series_uid
                        flash(f'Successfully processed {len(processed_studies)} DICOM files from archive!', 'success')
                        return redirect(url_for('gallery_viewer', series_uid=series_uid))
                    else:
                        flash(f'Successfully processed {len(processed_studies)} DICOM files!', 'success')
                        return redirect(url_for('index'))
            else:
                flash('No valid DICOM files were found in the uploaded files.', 'warning')

        except Exception as e:
            # Clean up on error
            multi_processor.cleanup_temp_files(temp_dirs)
            app.logger.error(f"Upload error: {str(e)}")
            flash(f'Upload error: {str(e)}', 'error')

        return redirect(request.url)

    return render_template('upload.html')

@app.route('/viewer/<study_uid>')
@login_required
def viewer(study_uid):
    """DICOM viewer page"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    # Update view count and last viewed
    study.view_count += 1
    study.last_viewed = datetime.utcnow()

    # Log access
    access_log = AccessLog(
        study_id=study.id,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent', '')
    )
    db.session.add(access_log)
    db.session.commit()

    return render_template('viewer.html', study=study, now=datetime.utcnow())

@app.route('/gallery/<series_uid>')
@login_required
def gallery_viewer(series_uid):
    """Gallery viewer for multiple DICOM files from the same series"""
    # Get all studies in this series
    studies = DicomStudy.query.filter_by(series_uid=series_uid).order_by(DicomStudy.series_number).all()

    if not studies:
        flash('Series not found', 'error')
        return redirect(url_for('index'))

    # Update view count for all studies in series
    for study in studies:
        study.view_count += 1
        study.last_viewed = datetime.utcnow()

        # Log access
        access_log = AccessLog(
            study_id=study.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(access_log)

    db.session.commit()

    return render_template('gallery.html', studies=studies, series_uid=series_uid, now=datetime.utcnow())

@app.route('/share/<study_uid>', methods=['GET', 'POST'])
def share_study(study_uid):
    """Generate shareable link and QR code for a study"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    if request.method == 'POST':
        # Generate share token and set expiration
        study.generate_share_token()
        study.is_shared = True

        # Set expiration (default 30 days)
        expiry_days = int(request.form.get('expiry_days', 30))
        study.share_expires = datetime.utcnow() + timedelta(days=expiry_days)

        db.session.commit()

        # Generate QR code
        share_url = url_for('shared_viewer', token=study.share_token, _external=True)
        qr_path = generate_qr_code(share_url, study.study_uid)

        flash('Shareable link generated successfully!', 'success')
        return render_template('share.html', study=study, share_url=share_url, qr_path=qr_path, now=datetime.utcnow())

    return render_template('share.html', study=study, now=datetime.utcnow())

@app.route('/shared/<token>')
def shared_viewer(token):
    """Public viewer for shared studies"""
    study = DicomStudy.query.filter_by(share_token=token).first_or_404()

    # Check if share has expired
    if study.share_expires and study.share_expires < datetime.utcnow():
        abort(410)  # Gone

    if not study.is_shared:
        abort(404)

    # Update view count and last viewed
    study.view_count += 1
    study.last_viewed = datetime.utcnow()

    # Log access
    access_log = AccessLog(
        study_id=study.id,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent', '')
    )
    db.session.add(access_log)
    db.session.commit()

    return render_template('viewer.html', study=study, is_shared=True, now=datetime.utcnow())

@app.route('/admin')
@login_required
def admin():
    """Admin dashboard for file management"""
    if not current_user.is_admin():
        flash('Access denied. Administrator privileges required.', 'error')
        return redirect(url_for('index'))

    studies = DicomStudy.query.order_by(DicomStudy.upload_date.desc()).all()
    total_users = User.query.count()
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()

    return render_template('admin.html',
                         studies=studies,
                         total_users=total_users,
                         recent_users=recent_users)

# User Management Routes
@app.route('/admin/users')
@login_required
def admin_users():
    """Admin user management page"""
    if not current_user.is_admin():
        flash('Access denied. Administrator privileges required.', 'error')
        return redirect(url_for('index'))

    users = User.query.order_by(User.created_at.desc()).all()
    return render_template('admin/users.html', users=users)

@app.route('/admin/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_edit_user(user_id):
    """Edit user details"""
    if not current_user.is_admin():
        flash('Access denied. Administrator privileges required.', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(user_id)
    form = UserEditForm(original_username=user.username, original_email=user.email, obj=user)

    if form.validate_on_submit():
        user.name = form.name.data
        user.username = form.username.data
        user.email = form.email.data
        user.user_type = form.user_type.data
        user.is_active = form.is_active.data

        db.session.commit()
        flash(f'User {user.username} has been updated successfully!', 'success')
        return redirect(url_for('admin_users'))

    return render_template('admin/edit_user.html', form=form, user=user)

@app.route('/admin/users/<int:user_id>/delete', methods=['POST'])
@login_required
def admin_delete_user(user_id):
    """Delete user"""
    if not current_user.is_admin():
        flash('Access denied. Administrator privileges required.', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(user_id)

    # Prevent deleting yourself
    if user.id == current_user.id:
        flash('You cannot delete your own account.', 'error')
        return redirect(url_for('admin_users'))

    username = user.username
    db.session.delete(user)
    db.session.commit()

    flash(f'User {username} has been deleted successfully!', 'success')
    return redirect(url_for('admin_users'))

@app.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile page"""
    form = ChangePasswordForm()

    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)
            db.session.commit()
            flash('Your password has been updated successfully!', 'success')
            return redirect(url_for('profile'))
        else:
            flash('Current password is incorrect.', 'error')

    return render_template('auth/profile.html', form=form)

@app.route('/update-report/<study_uid>', methods=['POST'])
def update_report(study_uid):
    """Update patient information and doctor's report"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    try:
        # Update patient information
        study.patient_age = request.form.get('patient_age', '').strip()
        study.patient_gender = request.form.get('patient_gender', '').strip()
        study.referring_physician = request.form.get('referring_physician', '').strip()

        # Update doctor's report
        study.diagnosis = request.form.get('diagnosis', '').strip()
        study.findings = request.form.get('findings', '').strip()
        study.impression = request.form.get('impression', '').strip()
        study.recommendations = request.form.get('recommendations', '').strip()
        study.reporting_physician = request.form.get('reporting_physician', '').strip()

        # Update report date if any report fields were filled
        if any([study.diagnosis, study.findings, study.impression, study.recommendations]):
            study.report_date = datetime.utcnow()

        db.session.commit()
        flash('Patient information and report updated successfully!', 'success')

    except Exception as e:
        logging.error(f"Error updating report: {str(e)}")
        flash(f'Error updating report: {str(e)}', 'error')

    return redirect(url_for('viewer', study_uid=study_uid))

@app.route('/delete/<study_uid>', methods=['POST'])
def delete_study(study_uid):
    """Delete a study and its associated file"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    try:
        # Delete file from filesystem
        if os.path.exists(study.file_path):
            os.remove(study.file_path)

        # Delete from database
        db.session.delete(study)
        db.session.commit()

        flash('Study deleted successfully!', 'success')
    except Exception as e:
        logging.error(f"Error deleting study: {str(e)}")
        flash(f'Error deleting study: {str(e)}', 'error')

    return redirect(url_for('admin'))

@app.route('/download/<study_uid>')
def download_study(study_uid):
    """Download DICOM file"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    if not os.path.exists(study.file_path):
        flash('File not found', 'error')
        return redirect(url_for('admin'))

    return send_file(study.file_path, as_attachment=True,
                    download_name=study.anonymized_filename)

@app.route('/api/dicom-data/<study_uid>', methods=['GET', 'OPTIONS'])
def get_dicom_data(study_uid):
    """API endpoint to serve DICOM data"""
    app.logger.info(f'Received request for DICOM data with study_uid: {study_uid}')

    if request.method == 'OPTIONS':
        response = app.make_default_options_response()
    else:
        app.logger.info(f'Looking up study in database...')
        study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()
        app.logger.info(f'Found study in database with file_path: {study.file_path}')

        try:
            # Handle both absolute and relative paths
            if os.path.isabs(study.file_path):
                file_path = study.file_path
            else:
                # If relative path, join with upload folder
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], study.file_path)

            # Normalize the path
            file_path = os.path.normpath(file_path)
            app.logger.info(f'Normalized file path: {file_path}')
            app.logger.info(f'Current working directory: {os.getcwd()}')
            app.logger.info(f'File exists check: {os.path.exists(file_path)}')
            app.logger.info(f'Upload folder path: {app.config["UPLOAD_FOLDER"]}')
            app.logger.info(f'Upload folder exists: {os.path.exists(app.config["UPLOAD_FOLDER"])}')

            # List files in upload folder for debugging
            if os.path.exists(app.config['UPLOAD_FOLDER']):
                files_in_upload = os.listdir(app.config['UPLOAD_FOLDER'])
                app.logger.info(f'Files in upload folder: {files_in_upload}')

                # Try to find the file by filename if exact path doesn't work
                if not os.path.exists(file_path):
                    filename = os.path.basename(study.file_path)
                    alternative_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    app.logger.info(f'Trying alternative path: {alternative_path}')
                    if os.path.exists(alternative_path):
                        file_path = alternative_path
                        app.logger.info(f'Found file at alternative path: {file_path}')

            if not os.path.exists(file_path):
                app.logger.error(f'DICOM file not found: {file_path}')
                app.logger.error(f'Study file_path from DB: {study.file_path}')
                app.logger.error(f'Anonymized filename: {study.anonymized_filename}')
                abort(404)

            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Create response with file content
            response = app.response_class(
                file_content,
                mimetype='application/dicom'
            )

            # Set content length
            response.headers['Content-Length'] = len(file_content)

        except IOError as e:
            app.logger.error(f'IO Error reading DICOM file: {str(e)}')
            abort(500)
        except Exception as e:
            app.logger.error(f'Error serving DICOM file: {str(e)}')
            abort(500)

    # Add CORS headers
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = '*'
    response.headers['Cache-Control'] = 'no-cache'

    return response

@app.route('/test-viewer')
def test_viewer():
    """Test viewer page for debugging"""
    return send_file('test_viewer.html')

# Trigger reload

@app.route('/api/dicom-image/<study_uid>')
def get_dicom_image(study_uid):
    """API endpoint to get DICOM image as base64-encoded image for simple viewer"""
    app.logger.info(f'Received request for DICOM image with study_uid: {study_uid}')

    try:
        study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()
        app.logger.info(f'Found study: {study.original_filename}')

        # Handle both absolute and relative paths
        if os.path.isabs(study.file_path):
            file_path = study.file_path
        else:
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], study.file_path)

        file_path = os.path.normpath(file_path)
        app.logger.info(f'Trying file path: {file_path}')

        # Try alternative path if file not found
        if not os.path.exists(file_path):
            filename = os.path.basename(study.file_path)
            alternative_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            app.logger.info(f'Trying alternative path: {alternative_path}')
            if os.path.exists(alternative_path):
                file_path = alternative_path
                app.logger.info(f'Using alternative path: {file_path}')

        if not os.path.exists(file_path):
            app.logger.error(f'DICOM file not found: {file_path}')
            app.logger.error(f'Study file_path from DB: {study.file_path}')
            app.logger.error(f'Upload folder: {app.config["UPLOAD_FOLDER"]}')

            # List files in upload folder for debugging
            if os.path.exists(app.config['UPLOAD_FOLDER']):
                files = os.listdir(app.config['UPLOAD_FOLDER'])
                app.logger.error(f'Files in upload folder: {files}')

            return jsonify({
                'error': 'DICOM file not found',
                'file_path': file_path,
                'study_uid': study_uid
            }), 404

        # Use DICOM processor to extract image data
        from utils.dicom_processor import DicomProcessor
        processor = DicomProcessor()

        app.logger.info(f'Processing DICOM file: {file_path}')
        image_data = processor.get_image_data(file_path)
        app.logger.info(f'Successfully processed DICOM image')

        return jsonify(image_data)

    except Exception as e:
        app.logger.error(f'Error processing DICOM image: {str(e)}')
        import traceback
        app.logger.error(f'Traceback: {traceback.format_exc()}')

        return jsonify({
            'error': 'Failed to process DICOM image',
            'message': str(e),
            'study_uid': study_uid
        }), 500

@app.route('/api/studies/<study_uid>/metadata')
def get_study_metadata(study_uid):
    """API endpoint to get study metadata in OHIF DICOMweb format"""
    study = DicomStudy.query.filter_by(study_uid=study_uid).first_or_404()

    try:
        metadata = {
            '00100020': { 'Value': [study.study_uid], 'vr': 'LO' },  # Patient ID
            '0020000D': { 'Value': [study.study_uid], 'vr': 'UI' },  # Study Instance UID
            '00080020': { 'Value': [study.study_date], 'vr': 'DA' },  # Study Date
            '00081030': { 'Value': [study.study_description], 'vr': 'LO' },  # Study Description
            '00080060': { 'Value': [study.modality], 'vr': 'CS' },  # Modality
            '00080080': { 'Value': [study.institution_name], 'vr': 'LO' },  # Institution Name
        }

        return jsonify(metadata)

    except Exception as e:
        app.logger.error(f'Error getting study metadata: {str(e)}')
        abort(500)

@app.errorhandler(404)
def not_found_error(error):
    return render_template('base.html', error_message="Page not found"), 404

@app.errorhandler(410)
def gone_error(error):
    return render_template('base.html', error_message="This shared link has expired"), 410

@app.errorhandler(413)
def too_large_error(error):
    return render_template('base.html', error_message="File too large. Maximum size is 500MB"), 413
