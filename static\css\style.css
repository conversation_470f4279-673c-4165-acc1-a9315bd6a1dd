:root {
  /* Modern Medical Theme Colors */
  --primary: #2563eb; /* medical blue */
  --primary-dark: #1d4ed8;
  --secondary: #64748b; /* slate gray */
  --success: #059669; /* medical green */
  --warning: #d97706; /* amber */
  --danger: #dc2626; /* red */
  --info: #0891b2; /* cyan */

  /* Background Colors */
  --background: #ffffff; /* clean white */
  --background-secondary: #f8fafc; /* light gray */
  --background-tertiary: #f1f5f9; /* lighter gray */
  --card-bg: #ffffff; /* white cards */

  /* Text Colors */
  --text: #1e293b; /* dark slate */
  --text-secondary: #475569; /* medium slate */
  --text-muted: #64748b; /* light slate */

  /* Border Colors */
  --border: #e2e8f0; /* light border */
  --border-light: #f1f5f9; /* very light border */

  /* Spacing and Effects */
  --spacing-unit: 16px;
  --border-radius: 8px;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  background: linear-gradient(
    135deg,
    var(--background-secondary) 0%,
    var(--background-tertiary) 100%
  );
  color: var(--text);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  margin-bottom: var(--spacing-unit);
  color: var(--text);
}

.text-muted {
  color: var(--text-muted) !important;
}

h1 {
  font-size: 2.5rem;
}
h2 {
  font-size: 2rem;
}
h3 {
  font-size: 1.5rem;
}
h4 {
  font-size: 1.25rem;
}

p {
  margin-bottom: var(--spacing-unit);
}

/* Navigation */
.navbar {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  ) !important;
  box-shadow: var(--shadow-lg);
  padding: 1rem 0;
  border: none;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: white !important;
  text-decoration: none;
}

.navbar-brand:hover {
  color: rgba(255, 255, 255, 0.9) !important;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border: none;
  border-radius: 1rem;
  box-shadow: var(--shadow);
  margin-bottom: var(--spacing-unit);
  transition: all 0.3s ease;
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--background-secondary);
  border-bottom: 1px solid var(--border);
  font-weight: 600;
  color: var(--text);
  padding: 1.25rem 1.5rem;
  border-radius: 1rem 1rem 0 0 !important;
}

.card-body {
  padding: 1.5rem;
}

/* Colored Card Headers */
.card-header.bg-primary {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  ) !important;
  color: white;
}

.card-header.bg-success {
  background: linear-gradient(
    135deg,
    var(--success) 0%,
    #047857 100%
  ) !important;
  color: white;
}

.card-header.bg-warning {
  background: linear-gradient(
    135deg,
    var(--warning) 0%,
    #b45309 100%
  ) !important;
  color: white;
}

.card-header.bg-danger {
  background: linear-gradient(
    135deg,
    var(--danger) 0%,
    #b91c1c 100%
  ) !important;
  color: white;
}

.card-header.bg-info {
  background: linear-gradient(135deg, var(--info) 0%, #0e7490 100%) !important;
  color: white;
}

/* Statistics Cards */
.card.bg-primary {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  ) !important;
  color: white;
}

.card.bg-success {
  background: linear-gradient(
    135deg,
    var(--success) 0%,
    #047857 100%
  ) !important;
  color: white;
}

.card.bg-warning {
  background: linear-gradient(
    135deg,
    var(--warning) 0%,
    #b45309 100%
  ) !important;
  color: white;
}

.card.bg-danger {
  background: linear-gradient(
    135deg,
    var(--danger) 0%,
    #b91c1c 100%
  ) !important;
  color: white;
}

.card.bg-info {
  background: linear-gradient(135deg, var(--info) 0%, #0e7490 100%) !important;
  color: white;
}

/* Buttons */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border: none;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  );
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, #1e40af 100%);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary) 0%, #475569 100%);
  color: white;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #475569 0%, #334155 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, var(--success) 0%, #047857 100%);
  color: white;
}

.btn-success:hover {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning) 0%, #b45309 100%);
  color: white;
}

.btn-warning:hover {
  background: linear-gradient(135deg, #b45309 0%, #92400e 100%);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger) 0%, #b91c1c 100%);
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, var(--info) 0%, #0e7490 100%);
  color: white;
}

.btn-info:hover {
  background: linear-gradient(135deg, #0e7490 0%, #155e75 100%);
  color: white;
}

/* Forms */
.form-control,
.form-select {
  border-radius: 0.5rem;
  border: 1px solid var(--border);
  transition: all 0.3s ease;
  background-color: var(--background);
  color: var(--text);
  padding: 0.75rem 1rem;
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
  background-color: var(--background);
  color: var(--text);
}

.form-label {
  font-weight: 600;
  color: var(--text);
  margin-bottom: 0.5rem;
}

/* Tables */
.table {
  border-radius: 0.75rem;
  overflow: hidden;
  background-color: var(--background);
  box-shadow: var(--shadow);
}

.table th {
  background-color: var(--background-secondary);
  border-top: none;
  font-weight: 600;
  color: var(--text);
  padding: 1rem;
}

.table td {
  padding: 1rem;
  color: var(--text);
  background-color: var(--background);
  border-top: 1px solid var(--border);
}

.table-hover tbody tr:hover {
  background-color: var(--background-secondary);
}

/* Badges */
.badge {
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
}

/* Dropdown */
.dropdown-menu {
  border-radius: 0.75rem;
  border: none;
  box-shadow: var(--shadow-lg);
  background: var(--background);
  padding: 0.5rem;
}

.dropdown-item {
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  color: var(--text);
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background-color: var(--background-secondary);
  color: var(--primary);
  transform: translateX(4px);
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-color: var(--border);
}

/* Alerts */
.alert {
  border-radius: 0.75rem;
  border: none;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: rgba(5, 150, 105, 0.1);
  color: var(--success);
  border-left: 4px solid var(--success);
}

.alert-danger {
  background-color: rgba(220, 38, 38, 0.1);
  color: var(--danger);
  border-left: 4px solid var(--danger);
}

.alert-warning {
  background-color: rgba(217, 119, 6, 0.1);
  color: var(--warning);
  border-left: 4px solid var(--warning);
}

.alert-info {
  background-color: rgba(8, 145, 178, 0.1);
  color: var(--info);
  border-left: 4px solid var(--info);
}

/* Footer */
.footer {
  background: linear-gradient(135deg, var(--text) 0%, #0f172a 100%);
  color: white;
  padding: 2rem 0;
  margin-top: 4rem;
}

.footer h5 {
  color: white;
  margin-bottom: 1rem;
}

.footer p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.footer small {
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .card {
    margin-bottom: 1rem;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .navbar-nav .nav-link {
    padding: 0.5rem !important;
    margin: 0.25rem 0;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Custom utilities */
.text-gradient {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Outline Buttons */
.btn-outline-primary {
  border: 2px solid var(--primary);
  color: var(--primary);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  color: white;
}

.btn-outline-secondary {
  border: 2px solid var(--secondary);
  color: var(--secondary);
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: var(--secondary);
  color: white;
}

.btn-outline-danger {
  border: 2px solid var(--danger);
  color: var(--danger);
  background-color: transparent;
}

.btn-outline-danger:hover {
  background-color: var(--danger);
  color: white;
}

/* Additional Styles for DICOM Platform */

/* Upload Area */
.upload-area {
  border: 3px dashed var(--border);
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  background-color: var(--background);
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: var(--primary);
  background-color: rgba(37, 99, 235, 0.05);
}

.upload-area.dragover {
  border-color: var(--primary);
  background-color: rgba(37, 99, 235, 0.1);
}

.upload-icon {
  font-size: 3rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

/* DICOM Viewer Styles */
.dicom-viewer {
  background-color: #000;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
}

.dicom-image {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

.viewer-controls {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 1rem;
  border-radius: 0.5rem;
  color: white;
}

.metadata-table {
  font-size: 0.9rem;
}

.metadata-table th {
  background-color: var(--background-secondary);
  color: var(--primary);
  font-weight: 600;
  width: 30%;
}

.metadata-table td {
  background-color: var(--background);
  color: var(--text);
}

/* QR Code Display */
.qr-code-container {
  text-align: center;
  padding: 2rem;
  background-color: var(--background);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  box-shadow: var(--shadow);
}

.qr-code-container img {
  max-width: 200px;
  height: auto;
}

/* Loading Spinner */
.spinner {
  border: 4px solid var(--border);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 2rem auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Small Button Styles */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  margin: 0 0.25rem;
}
