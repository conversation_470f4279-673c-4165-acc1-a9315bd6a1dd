// Initialize Cornerstone with error handling
try {
  if (
    typeof cornerstone !== "undefined" &&
    typeof cornerstoneWADOImageLoader !== "undefined"
  ) {
    cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
    cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
    cornerstoneTools.external.cornerstone = cornerstone;
    cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
    cornerstoneTools.init();
    console.log("Cornerstone initialized successfully");
  } else {
    console.error("Cornerstone libraries not loaded properly");
  }
} catch (error) {
  console.error("Error initializing Cornerstone:", error);
}

class DicomViewer {
  constructor(elementId, studyUid) {
    this.element = document.getElementById(elementId);
    this.studyUid = studyUid;
    this.toolState = "none";
    this.imageId = null;

    this.init();
  }

  async init() {
    try {
      console.log("Initializing DICOM viewer for study:", this.studyUid);

      // Check if Cornerstone is available
      if (typeof cornerstone === "undefined") {
        throw new Error("Cornerstone library not loaded");
      }

      // Enable the element for Cornerstone
      cornerstone.enable(this.element);
      console.log("Cornerstone element enabled");

      // Initialize tools
      this.initializeTools();

      // Load the DICOM image
      await this.loadImage();
    } catch (error) {
      console.error("Error initializing DICOM viewer:", error);
      this.showError(`Failed to initialize viewer: ${error.message}`);
    }
  }

  initializeTools() {
    // Initialize tools
    cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
    cornerstoneTools.addTool(cornerstoneTools.PanTool);
    cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
    cornerstoneTools.addTool(cornerstoneTools.LengthTool);
    cornerstoneTools.addTool(cornerstoneTools.AngleTool);

    // Set the default tool
    cornerstoneTools.setToolActive("Wwwc", { mouseButtonMask: 1 });
  }

  async loadImage() {
    try {
      console.log("Loading DICOM image for study:", this.studyUid);

      // Configure cornerstone for direct loading
      const baseUrl = window.location.origin;
      const url = `${baseUrl}/api/dicom-data/${this.studyUid}`;
      console.log("DICOM data URL:", url);

      // Test if the API endpoint is accessible
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(
          `API request failed: ${response.status} ${response.statusText}`
        );
      }
      console.log(
        "API endpoint accessible, content-type:",
        response.headers.get("content-type")
      );

      // Use the WADO URI loader which is more robust
      this.imageId = `wadouri:${url}`;
      console.log("Image ID:", this.imageId);

      // Configure the WADO image loader
      cornerstoneWADOImageLoader.configure({
        beforeSend: function (xhr) {
          console.log("Sending WADO request");
        },
      });

      // Load and display the image
      console.log("Loading image with Cornerstone...");
      const image = await cornerstone.loadAndCacheImage(this.imageId);
      console.log("Image loaded:", image);

      cornerstone.displayImage(this.element, image);
      console.log("Image displayed");

      // Add the loaded image to the tool state
      cornerstoneTools.addStackStateManager(this.element, ["stack"]);

      console.log("DICOM image loaded successfully");
    } catch (error) {
      console.error("Error loading DICOM image:", error);
      this.showError(`Failed to load DICOM image: ${error.message}`);
    }
  }

  resetImage() {
    cornerstone.reset(this.element);
  }

  toggleWindowLevel() {
    this.setActiveTool("Wwwc");
  }

  togglePan() {
    this.setActiveTool("Pan");
  }

  toggleZoom() {
    this.setActiveTool("Zoom");
  }

  invertImage() {
    const viewport = cornerstone.getViewport(this.element);
    viewport.invert = !viewport.invert;
    cornerstone.setViewport(this.element, viewport);
  }

  setActiveTool(toolName) {
    if (this.toolState === toolName) {
      // Disable the tool if it's already active
      cornerstoneTools.setToolDisabled(toolName);
      this.toolState = "none";
    } else {
      // Enable the new tool
      cornerstoneTools.setToolActive(toolName, { mouseButtonMask: 1 });
      this.toolState = toolName;
    }
  }

  showError(message) {
    const errorDiv = document.createElement("div");
    errorDiv.className = "alert alert-danger m-3";
    errorDiv.textContent = message;
    this.element.appendChild(errorDiv);
  }
}

// Fallback viewer using simple image display
class SimpleDicomViewer {
  constructor(elementId, studyUid) {
    this.element = document.getElementById(elementId);
    this.studyUid = studyUid;
    this.init();
  }

  async init() {
    try {
      console.log("Initializing simple DICOM viewer for study:", this.studyUid);

      // Create loading indicator
      this.element.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
          <div class="text-center">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading DICOM image...</p>
          </div>
        </div>
      `;

      // Load image data from API
      const response = await fetch(`/api/dicom-image/${this.studyUid}`);
      if (!response.ok) {
        throw new Error(`Failed to load image: ${response.status}`);
      }

      const imageData = await response.json();
      this.displayImage(imageData);
    } catch (error) {
      console.error("Error in simple viewer:", error);
      this.showError(`Failed to load image: ${error.message}`);
    }
  }

  displayImage(imageData) {
    this.element.innerHTML = `
      <div class="text-center">
        <img src="${imageData.image}"
             class="img-fluid"
             style="max-width: 100%; max-height: 600px; object-fit: contain;"
             alt="DICOM Image">
      </div>
    `;
    console.log("Simple viewer: Image displayed successfully");
  }

  showError(message) {
    this.element.innerHTML = `
      <div class="alert alert-danger m-3">
        <h6>Viewer Error</h6>
        <p>${message}</p>
        <small>Please try refreshing the page or contact support.</small>
      </div>
    `;
  }

  // Stub methods for compatibility
  resetImage() {
    console.log("Reset not available in simple viewer");
  }
  toggleWindowLevel() {
    console.log("Window/Level not available in simple viewer");
  }
  togglePan() {
    console.log("Pan not available in simple viewer");
  }
  toggleZoom() {
    console.log("Zoom not available in simple viewer");
  }
  invertImage() {
    console.log("Invert not available in simple viewer");
  }
}

// Initialize viewer when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  const element = document.getElementById("dicom-image");
  if (element) {
    const studyUid = element.getAttribute("data-study-uid");
    if (studyUid) {
      // Try Cornerstone viewer first, fallback to simple viewer
      if (
        typeof cornerstone !== "undefined" &&
        typeof cornerstoneWADOImageLoader !== "undefined"
      ) {
        console.log("Using Cornerstone viewer");
        window.viewer = new DicomViewer("dicom-image", studyUid);
      } else {
        console.log("Cornerstone not available, using simple viewer");
        window.viewer = new SimpleDicomViewer("dicom-image", studyUid);
      }
    }
  }
});
