{% extends "base.html" %} {% block title %}Admin Dashboard - DICOM Platform{%
endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1>
        <i class="fas fa-tachometer-alt me-2"></i>
        Admin Dashboard
      </h1>
      <div>
        <a href="{{ url_for('admin_users') }}" class="btn btn-info me-2">
          <i class="fas fa-users me-1"></i>
          Manage Users
        </a>
        <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
          <i class="fas fa-plus me-1"></i>
          Upload New Study
        </a>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ studies|length }}</h4>
                <p class="mb-0">Total Studies</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-file-medical fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">
                  {{ studies|selectattr('is_shared')|list|length }}
                </h4>
                <p class="mb-0">Shared Studies</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-share-alt fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ total_users }}</h4>
                <p class="mb-0">Total Users</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-users fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">
                  {{ "%.1f"|format((studies|sum(attribute='file_size')) / 1024 /
                  1024) }}
                </h4>
                <p class="mb-0">Storage (MB)</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-hdd fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>Recent Users</h5>
          </div>
          <div class="card-body">
            {% if recent_users %}
            <div class="list-group list-group-flush">
              {% for user in recent_users %}
              <div
                class="list-group-item d-flex justify-content-between align-items-center px-0"
              >
                <div>
                  <strong>{{ user.name }}</strong>
                  <br /><small class="text-muted"
                    >@{{ user.username }} - {{ user.user_type.title() }}</small
                  >
                </div>
                <span
                  class="badge bg-{{ 'success' if user.is_active else 'secondary' }}"
                >
                  {{ 'Active' if user.is_active else 'Inactive' }}
                </span>
              </div>
              {% endfor %}
            </div>
            <div class="text-center mt-3">
              <a
                href="{{ url_for('admin_users') }}"
                class="btn btn-sm btn-outline-primary"
              >
                View All Users
              </a>
            </div>
            {% else %}
            <p class="text-muted text-center">No users found</p>
            {% endif %}
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Quick Actions</h5>
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <a href="{{ url_for('register') }}" class="btn btn-success">
                <i class="fas fa-user-plus me-2"></i>Add New User
              </a>
              <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
                <i class="fas fa-upload me-2"></i>Upload DICOM Study
              </a>
              <button class="btn btn-warning" onclick="cleanupExpiredShares()">
                <i class="fas fa-clock me-2"></i>Cleanup Expired Shares
              </button>
              <button class="btn btn-info" onclick="exportStudiesList()">
                <i class="fas fa-download me-2"></i>Export Studies List
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
  <div class="col-md-6">
    <div class="input-group">
      <span class="input-group-text">
        <i data-feather="search"></i>
      </span>
      <input
        type="text"
        class="form-control"
        id="study-search"
        placeholder="Search studies by filename, description, or modality..."
      />
    </div>
  </div>
  <div class="col-md-6 text-md-end">
    <div class="btn-group">
      <button
        type="button"
        class="btn btn-outline-secondary dropdown-toggle"
        data-bs-toggle="dropdown"
      >
        <i data-feather="filter" class="me-1"></i>
        Filter
      </button>
      <ul class="dropdown-menu">
        <li>
          <a class="dropdown-item" href="#" onclick="filterByStatus('all')"
            >All Studies</a
          >
        </li>
        <li>
          <a class="dropdown-item" href="#" onclick="filterByStatus('shared')"
            >Shared Only</a
          >
        </li>
        <li>
          <a class="dropdown-item" href="#" onclick="filterByStatus('private')"
            >Private Only</a
          >
        </li>
        <li><hr class="dropdown-divider" /></li>
        <li>
          <a class="dropdown-item" href="#" onclick="sortBy('date')"
            >Sort by Date</a
          >
        </li>
        <li>
          <a class="dropdown-item" href="#" onclick="sortBy('views')"
            >Sort by Views</a
          >
        </li>
      </ul>
    </div>
  </div>
</div>

<!-- Studies Table -->
<div class="card">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h4 class="mb-0">
        <i data-feather="folder" class="me-2"></i>
        All Studies ({{ studies|length }})
      </h4>
      <div class="text-muted">
        <small
          >Total storage: {{ "%.2f"|format((studies|sum(attribute='file_size'))
          / 1024 / 1024) }} MB</small
        >
      </div>
    </div>
  </div>
  <div class="card-body p-0">
    {% if studies %}
    <div class="table-responsive">
      <table class="table table-hover mb-0" id="studies-table">
        <thead>
          <tr>
            <th style="width: 30%">Study</th>
            <th style="width: 15%">Modality</th>
            <th style="width: 15%">Upload Date</th>
            <th style="width: 10%">Size</th>
            <th style="width: 10%">Status</th>
            <th style="width: 10%">Views</th>
            <th style="width: 10%">Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for study in studies %}
          <tr>
            <td>
              <div class="d-flex align-items-center">
                <i
                  data-feather="file-text"
                  class="me-3 text-primary flex-shrink-0"
                ></i>
                <div class="min-w-0">
                  <div class="fw-semibold text-truncate">
                    {{ study.study_description or 'Untitled Study' }}
                  </div>
                  <div class="text-muted small text-truncate">
                    {{ study.original_filename }}
                  </div>
                  <div class="text-muted small">
                    ID: {{ study.study_uid[:16] }}...
                  </div>
                </div>
              </div>
            </td>
            <td>
              {% if study.modality %}
              <span class="badge badge-secondary">{{ study.modality }}</span>
              {% else %}
              <span class="text-muted">Unknown</span>
              {% endif %}
            </td>
            <td>
              <div>{{ study.upload_date.strftime('%Y-%m-%d') }}</div>
              <small class="text-muted"
                >{{ study.upload_date.strftime('%H:%M') }}</small
              >
            </td>
            <td>
              <span class="text-muted"
                >{{ "%.1f"|format(study.file_size / 1024 / 1024) }} MB</span
              >
            </td>
            <td>
              {% if study.is_shared %}
              <span class="badge badge-success" title="Shared publicly">
                <i data-feather="share-2" style="width: 12px; height: 12px"></i>
                Shared
              </span>
              {% if study.share_expires and study.share_expires < current_time
              %}
              <div class="text-warning small">Expired</div>
              {% endif %} {% else %}
              <span class="badge badge-secondary">Private</span>
              {% endif %}
            </td>
            <td>
              <div class="d-flex align-items-center">
                <i
                  data-feather="eye"
                  class="me-1 text-muted"
                  style="width: 14px; height: 14px"
                ></i>
                {{ study.view_count }}
              </div>
              {% if study.last_viewed %}
              <small class="text-muted"
                >{{ study.last_viewed.strftime('%m/%d') }}</small
              >
              {% endif %}
            </td>
            <td>
              <div class="btn-group btn-group-sm">
                <a
                  href="{{ url_for('viewer', study_uid=study.study_uid) }}"
                  class="btn btn-outline-primary"
                  title="View Study"
                  data-bs-toggle="tooltip"
                >
                  <i data-feather="eye" style="width: 14px; height: 14px"></i>
                </a>
                <a
                  href="{{ url_for('share_study', study_uid=study.study_uid) }}"
                  class="btn btn-outline-secondary"
                  title="Share Study"
                  data-bs-toggle="tooltip"
                >
                  <i
                    data-feather="share-2"
                    style="width: 14px; height: 14px"
                  ></i>
                </a>
                <a
                  href="{{ url_for('download_study', study_uid=study.study_uid) }}"
                  class="btn btn-outline-info"
                  title="Download Study"
                  data-bs-toggle="tooltip"
                >
                  <i
                    data-feather="download"
                    style="width: 14px; height: 14px"
                  ></i>
                </a>
                <form
                  method="POST"
                  action="{{ url_for('delete_study', study_uid=study.study_uid) }}"
                  class="d-inline"
                >
                  <button
                    type="submit"
                    class="btn btn-outline-danger delete-confirm"
                    title="Delete Study"
                    data-bs-toggle="tooltip"
                    data-study-name="{{ study.study_description or study.original_filename }}"
                  >
                    <i
                      data-feather="trash-2"
                      style="width: 14px; height: 14px"
                    ></i>
                  </button>
                </form>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    {% else %}
    <div class="text-center py-5">
      <i
        data-feather="inbox"
        class="text-muted mb-3"
        style="width: 64px; height: 64px"
      ></i>
      <h4 class="text-muted">No studies found</h4>
      <p class="text-muted mb-4">Upload your first DICOM file to get started</p>
      <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
        <i data-feather="upload" class="me-1"></i>
        Upload DICOM File
      </a>
    </div>
    {% endif %}
  </div>
</div>

<!-- Bulk Actions (if studies exist) -->
{% if studies %}
<div class="card mt-4">
  <div class="card-header">
    <h5 class="mb-0">
      <i data-feather="tool" class="me-2"></i>
      Bulk Actions
    </h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <div class="alert alert-info">
          <i data-feather="info" class="me-2"></i>
          <strong>Storage Usage:</strong>
          {{ "%.2f"|format((studies|sum(attribute='file_size')) / 1024 / 1024)
          }} MB used across {{ studies|length }} studies
        </div>
      </div>
      <div class="col-md-6">
        <div class="alert alert-success">
          <i data-feather="share-2" class="me-2"></i>
          <strong>Sharing Stats:</strong>
          {{ studies|selectattr('is_shared')|list|length }} studies shared
          publicly
        </div>
      </div>
    </div>

    <div class="text-center">
      <button
        class="btn btn-outline-warning me-2"
        onclick="cleanupExpiredShares()"
      >
        <i data-feather="clock" class="me-1"></i>
        Cleanup Expired Shares
      </button>
      <button class="btn btn-outline-info" onclick="exportStudiesList()">
        <i data-feather="download" class="me-1"></i>
        Export Studies List
      </button>
    </div>
  </div>
</div>
{% endif %}

<script>
  // Filter and sort functions
  function filterByStatus(status) {
    const rows = document.querySelectorAll("#studies-table tbody tr");
    rows.forEach((row) => {
      const statusCell = row.cells[4];
      const isShared = statusCell.textContent.includes("Shared");

      let show = true;
      if (status === "shared" && !isShared) show = false;
      if (status === "private" && isShared) show = false;

      row.style.display = show ? "" : "none";
    });
  }

  function sortBy(criteria) {
    // Implementation for sorting would go here
    console.log("Sort by:", criteria);
  }

  function cleanupExpiredShares() {
    if (confirm("Remove sharing for all expired studies?")) {
      // Implementation would go here
      alert("Expired shares cleaned up!");
    }
  }

  function exportStudiesList() {
    // Implementation for CSV export would go here
    alert("Studies list exported!");
  }

  // Search functionality is handled in main.js
</script>
{% endblock %}
