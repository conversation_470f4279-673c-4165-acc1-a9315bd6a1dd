{% extends "base.html" %}

{% block title %}User Management - DICOM Viewer{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users"></i> User Management</h2>
        <div>
            <a href="{{ url_for('register') }}" class="btn btn-success">
                <i class="fas fa-user-plus"></i> Add New User
            </a>
            <a href="{{ url_for('admin') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Admin
            </a>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> All Users ({{ users|length }})</h5>
        </div>
        <div class="card-body p-0">
            {% if users %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Name</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <i class="fas fa-user-{{ 'shield' if user.is_admin() else 'circle' }}"></i>
                                    </div>
                                    <strong>{{ user.name }}</strong>
                                </div>
                            </td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                <span class="badge bg-{{ 'danger' if user.is_admin() else 'primary' }}">
                                    {{ user.user_type.title() }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                    {{ 'Active' if user.is_active else 'Inactive' }}
                                </span>
                            </td>
                            <td>{{ user.created_at.strftime('%m/%d/%Y') }}</td>
                            <td>
                                {% if user.last_login %}
                                    {{ user.last_login.strftime('%m/%d/%Y %I:%M %p') }}
                                {% else %}
                                    <span class="text-muted">Never</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('admin_edit_user', user_id=user.id) }}" 
                                       class="btn btn-outline-primary" title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if user.id != current_user.id %}
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmDelete('{{ user.username }}', {{ user.id }})" 
                                            title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No users found</h5>
                <p class="text-muted">Start by adding your first user.</p>
                <a href="{{ url_for('register') }}" class="btn btn-success">
                    <i class="fas fa-user-plus"></i> Add First User
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle"></i> Confirm Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUsername"></strong>?</p>
                <p class="text-danger"><i class="fas fa-warning"></i> This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>

<script>
function confirmDelete(username, userId) {
    document.getElementById('deleteUsername').textContent = username;
    document.getElementById('deleteForm').action = '/admin/users/' + userId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
