{% extends "base.html" %}

{% block title %}Profile - DICOM Viewer{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-user"></i> Profile Information</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle">
                            <i class="fas fa-user-md"></i>
                        </div>
                    </div>
                    
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ current_user.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Username:</strong></td>
                            <td>{{ current_user.username }}</td>
                        </tr>
                        <tr>
                            <td><strong>Email:</strong></td>
                            <td>{{ current_user.email }}</td>
                        </tr>
                        <tr>
                            <td><strong>Role:</strong></td>
                            <td>
                                <span class="badge bg-{{ 'danger' if current_user.is_admin() else 'primary' }}">
                                    {{ current_user.user_type.title() }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Member Since:</strong></td>
                            <td>{{ current_user.created_at.strftime('%B %d, %Y') }}</td>
                        </tr>
                        {% if current_user.last_login %}
                        <tr>
                            <td><strong>Last Login:</strong></td>
                            <td>{{ current_user.last_login.strftime('%B %d, %Y at %I:%M %p') }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-key"></i> Change Password</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.current_password.label(class="form-label") }}
                            {{ form.current_password(class="form-control") }}
                            {% if form.current_password.errors %}
                                <div class="text-danger">
                                    {% for error in form.current_password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.new_password.label(class="form-label") }}
                                {{ form.new_password(class="form-control") }}
                                {% if form.new_password.errors %}
                                    <div class="text-danger">
                                        {% for error in form.new_password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.new_password2.label(class="form-label") }}
                                {{ form.new_password2(class="form-control") }}
                                {% if form.new_password2.errors %}
                                    <div class="text-danger">
                                        {% for error in form.new_password2.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            {{ form.submit(class="btn btn-warning") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    font-size: 2rem;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.form-control:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    color: #000;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #e67e00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: #000;
}
</style>
{% endblock %}
