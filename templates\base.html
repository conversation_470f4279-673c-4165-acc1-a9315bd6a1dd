<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}DICOM Platform{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- Font Awesome Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Feather Icons -->
    <script src="https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.js"></script>

    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Custom CSS -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
      <div class="container">
        <a class="navbar-brand" href="{{ url_for('index') }}">
          <i data-feather="heart" class="me-2"></i>
          DICOM Platform
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          {% if current_user.is_authenticated %}
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a
                class="nav-link {% if request.endpoint == 'index' %}active{% endif %}"
                href="{{ url_for('index') }}"
              >
                <i class="fas fa-home me-1"></i> Dashboard
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link {% if request.endpoint == 'upload_file' %}active{% endif %}"
                href="{{ url_for('upload_file') }}"
              >
                <i class="fas fa-upload me-1"></i> Upload
              </a>
            </li>
            {% if current_user.is_admin() %}
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle {% if request.endpoint in ['admin', 'admin_users'] %}active{% endif %}"
                href="#"
                id="adminDropdown"
                role="button"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-cog me-1"></i> Admin
              </a>
              <ul class="dropdown-menu">
                <li>
                  <a class="dropdown-item" href="{{ url_for('admin') }}">
                    <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users me-2"></i> User Management
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="{{ url_for('register') }}">
                    <i class="fas fa-user-plus me-2"></i> Add User
                  </a>
                </li>
              </ul>
            </li>
            {% endif %}
          </ul>

          <ul class="navbar-nav">
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="userDropdown"
                role="button"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-user-circle me-1"></i> {{ current_user.name }}
                <span
                  class="badge bg-{{ 'danger' if current_user.is_admin() else 'primary' }} ms-1"
                >
                  {{ current_user.user_type.title() }}
                </span>
              </a>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <a class="dropdown-item" href="{{ url_for('profile') }}">
                    <i class="fas fa-user me-2"></i> Profile
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-2"></i> Logout
                  </a>
                </li>
              </ul>
            </li>
          </ul>
          {% else %}
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('login') }}">
                <i class="fas fa-sign-in-alt me-1"></i> Login
              </a>
            </li>
          </ul>
          {% endif %}
        </div>
      </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %} {% if
    messages %}
    <div class="container mt-3" id="alert-container">
      {% for category, message in messages %}
      <div
        class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show"
        role="alert"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
        ></button>
      </div>
      {% endfor %}
    </div>
    {% endif %} {% endwith %}

    <!-- Main Content -->
    <main class="container my-4">
      {% if error_message %}
      <div class="text-center py-5">
        <div class="card">
          <div class="card-body">
            <i
              data-feather="alert-triangle"
              class="text-warning mb-3"
              style="width: 64px; height: 64px"
            ></i>
            <h3 class="text-warning">{{ error_message }}</h3>
            <a href="{{ url_for('index') }}" class="btn btn-primary mt-3"
              >Go Home</a
            >
          </div>
        </div>
      </div>
      {% else %} {% block content %}{% endblock %} {% endif %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <h5>DICOM Platform</h5>
            <p class="mb-0">Secure medical image sharing for professionals</p>
          </div>
          <div class="col-md-6 text-md-end">
            <p class="mb-0">
              Built with
              <i
                data-feather="heart"
                style="width: 16px; height: 16px; color: #dd6b20"
              ></i>
              for healthcare
            </p>
            <small class="text-muted"
              >Professional medical imaging platform</small
            >
          </div>
        </div>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- DICOM Viewer JS -->
    {% block scripts %}{% endblock %}

    <!-- Initialize Feather Icons -->
    <script>
      feather.replace();
    </script>
  </body>
</html>
