{% extends "base.html" %}

{% block title %}DICOM Gallery - {{ studies[0].patient_name or 'Anonymous' }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="fas fa-images me-2"></i>
                                DICOM Gallery - {{ studies|length }} Images
                            </h4>
                            <small>Series: {{ series_uid[:8] }}...</small>
                        </div>
                        <div>
                            <a href="{{ url_for('index') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>
                                Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user me-2"></i>Patient Information</h6>
                            <p class="mb-1"><strong>Name:</strong> {{ studies[0].patient_name or 'Anonymous' }}</p>
                            <p class="mb-1"><strong>ID:</strong> {{ studies[0].patient_id or 'N/A' }}</p>
                            <p class="mb-1"><strong>Sex:</strong> {{ studies[0].patient_sex or 'N/A' }}</p>
                            <p class="mb-0"><strong>Age:</strong> {{ studies[0].patient_age or 'N/A' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-stethoscope me-2"></i>Study Information</h6>
                            <p class="mb-1"><strong>Study Date:</strong> {{ studies[0].study_date or 'N/A' }}</p>
                            <p class="mb-1"><strong>Modality:</strong> {{ studies[0].modality or 'N/A' }}</p>
                            <p class="mb-1"><strong>Description:</strong> {{ studies[0].study_description or 'N/A' }}</p>
                            <p class="mb-0"><strong>Institution:</strong> {{ studies[0].institution_name or 'N/A' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gallery Controls -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="grid-view-btn">
                                <i class="fas fa-th me-1"></i>Grid View
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="list-view-btn">
                                <i class="fas fa-list me-1"></i>List View
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="compare-view-btn">
                                <i class="fas fa-columns me-1"></i>Compare View
                            </button>
                        </div>
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0">Zoom:</label>
                            <input type="range" class="form-range" id="zoom-slider" min="0.5" max="3" step="0.1" value="1" style="width: 150px;">
                            <span class="ms-2" id="zoom-value">100%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gallery Content -->
    <div class="row" id="gallery-container">
        <!-- Grid View (Default) -->
        <div id="grid-view" class="col-12">
            <div class="row">
                {% for study in studies %}
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4 gallery-item" data-study-uid="{{ study.study_uid }}">
                    <div class="card h-100 study-card">
                        <div class="card-header py-2">
                            <small class="text-muted">
                                <i class="fas fa-file-medical me-1"></i>
                                Image {{ study.series_number }}/{{ study.total_series_files }}
                            </small>
                        </div>
                        <div class="card-body p-2">
                            <div class="dicom-thumbnail" data-study-uid="{{ study.study_uid }}">
                                <div class="loading-spinner">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <small class="d-block">Loading...</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ study.original_filename.split(':')[-1] if ':' in study.original_filename else study.original_filename }}</small>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary btn-sm view-btn" data-study-uid="{{ study.study_uid }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm compare-btn" data-study-uid="{{ study.study_uid }}">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Compare View -->
        <div id="compare-view" class="col-12" style="display: none;">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Image A</h6>
                            <select class="form-select form-select-sm mt-2" id="compare-select-a">
                                <option value="">Select image...</option>
                                {% for study in studies %}
                                <option value="{{ study.study_uid }}">Image {{ study.series_number }} - {{ study.original_filename.split(':')[-1] if ':' in study.original_filename else study.original_filename }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="card-body">
                            <div class="dicom-viewer" id="compare-viewer-a">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-image fa-3x mb-3"></i>
                                    <p>Select an image to compare</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Image B</h6>
                            <select class="form-select form-select-sm mt-2" id="compare-select-b">
                                <option value="">Select image...</option>
                                {% for study in studies %}
                                <option value="{{ study.study_uid }}">Image {{ study.series_number }} - {{ study.original_filename.split(':')[-1] if ':' in study.original_filename else study.original_filename }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="card-body">
                            <div class="dicom-viewer" id="compare-viewer-b">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-image fa-3x mb-3"></i>
                                    <p>Select an image to compare</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Full Screen Modal -->
    <div class="modal fade" id="fullscreen-modal" tabindex="-1">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">DICOM Viewer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0">
                    <div id="fullscreen-viewer" class="w-100 h-100">
                        <!-- DICOM viewer will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.gallery-item {
    transition: transform 0.2s ease;
}

.gallery-item:hover {
    transform: translateY(-2px);
}

.study-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.study-card:hover {
    border-color: var(--primary);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
}

.study-card.selected {
    border-color: var(--success);
    background-color: rgba(34, 197, 94, 0.05);
}

.dicom-thumbnail {
    height: 200px;
    background-color: #000;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.dicom-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.dicom-viewer {
    height: 400px;
    background-color: #000;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.dicom-viewer img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-align: center;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.form-range {
    cursor: pointer;
}

#fullscreen-viewer {
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

#fullscreen-viewer img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}
</style>

<script>
// Gallery functionality
document.addEventListener('DOMContentLoaded', function() {
    const gridViewBtn = document.getElementById('grid-view-btn');
    const listViewBtn = document.getElementById('list-view-btn');
    const compareViewBtn = document.getElementById('compare-view-btn');
    const gridView = document.getElementById('grid-view');
    const compareView = document.getElementById('compare-view');
    const zoomSlider = document.getElementById('zoom-slider');
    const zoomValue = document.getElementById('zoom-value');
    
    let selectedImages = new Set();
    let currentZoom = 1;
    
    // Load thumbnails
    loadThumbnails();
    
    // View switching
    gridViewBtn.addEventListener('click', () => switchView('grid'));
    compareViewBtn.addEventListener('click', () => switchView('compare'));
    
    // Zoom control
    zoomSlider.addEventListener('input', function() {
        currentZoom = parseFloat(this.value);
        zoomValue.textContent = Math.round(currentZoom * 100) + '%';
        applyZoom();
    });
    
    // Compare selects
    document.getElementById('compare-select-a').addEventListener('change', function() {
        loadCompareImage('a', this.value);
    });
    
    document.getElementById('compare-select-b').addEventListener('change', function() {
        loadCompareImage('b', this.value);
    });
    
    function switchView(view) {
        // Update button states
        document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
        
        if (view === 'grid') {
            gridViewBtn.classList.add('active');
            gridView.style.display = 'block';
            compareView.style.display = 'none';
        } else if (view === 'compare') {
            compareViewBtn.classList.add('active');
            gridView.style.display = 'none';
            compareView.style.display = 'block';
        }
    }
    
    function loadThumbnails() {
        document.querySelectorAll('.dicom-thumbnail').forEach(thumbnail => {
            const studyUid = thumbnail.dataset.studyUid;
            loadDicomImage(studyUid, thumbnail);
        });
    }
    
    function loadDicomImage(studyUid, container) {
        fetch(`/api/dicom-image/${studyUid}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    container.innerHTML = `
                        <div class="loading-spinner">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            <small class="d-block">Error loading image</small>
                        </div>
                    `;
                } else {
                    const img = document.createElement('img');
                    img.src = `data:image/png;base64,${data.image}`;
                    img.alt = 'DICOM Image';
                    container.innerHTML = '';
                    container.appendChild(img);
                }
            })
            .catch(error => {
                console.error('Error loading DICOM image:', error);
                container.innerHTML = `
                    <div class="loading-spinner">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                        <small class="d-block">Failed to load</small>
                    </div>
                `;
            });
    }
    
    function loadCompareImage(side, studyUid) {
        if (!studyUid) return;
        
        const viewer = document.getElementById(`compare-viewer-${side}`);
        viewer.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <small class="d-block">Loading...</small>
            </div>
        `;
        
        loadDicomImage(studyUid, viewer);
    }
    
    function applyZoom() {
        document.querySelectorAll('.dicom-thumbnail img, .dicom-viewer img').forEach(img => {
            img.style.transform = `scale(${currentZoom})`;
        });
    }
    
    // Thumbnail click handlers
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const studyUid = this.dataset.studyUid;
            window.open(`/viewer/${studyUid}`, '_blank');
        });
    });
    
    // Compare button handlers
    document.querySelectorAll('.compare-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const studyUid = this.dataset.studyUid;
            const card = this.closest('.study-card');
            
            if (selectedImages.has(studyUid)) {
                selectedImages.delete(studyUid);
                card.classList.remove('selected');
                this.innerHTML = '<i class="fas fa-plus"></i>';
            } else if (selectedImages.size < 2) {
                selectedImages.add(studyUid);
                card.classList.add('selected');
                this.innerHTML = '<i class="fas fa-check"></i>';
            }
            
            // Auto-switch to compare view if 2 images selected
            if (selectedImages.size === 2) {
                switchView('compare');
                const images = Array.from(selectedImages);
                document.getElementById('compare-select-a').value = images[0];
                document.getElementById('compare-select-b').value = images[1];
                loadCompareImage('a', images[0]);
                loadCompareImage('b', images[1]);
            }
        });
    });
    
    // Thumbnail click for fullscreen
    document.querySelectorAll('.dicom-thumbnail').forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            const studyUid = this.dataset.studyUid;
            const modal = new bootstrap.Modal(document.getElementById('fullscreen-modal'));
            const viewer = document.getElementById('fullscreen-viewer');
            
            viewer.innerHTML = `
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    <small class="d-block">Loading...</small>
                </div>
            `;
            
            modal.show();
            loadDicomImage(studyUid, viewer);
        });
    });
    
    // Initialize with grid view
    switchView('grid');
});
</script>
{% endblock %}
