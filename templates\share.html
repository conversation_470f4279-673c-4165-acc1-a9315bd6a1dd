{% extends "base.html" %} {% block title %}Share Study - DICOM Platform{%
endblock %} {% block content %}
<div class="row justify-content-center">
  <div class="col-lg-8">
    <div class="card">
      <div class="card-header">
        <h3 class="mb-0">
          <i data-feather="share-2" class="me-2"></i>
          Share DICOM Study
        </h3>
      </div>
      <div class="card-body">
        <!-- Study Information -->
        <div class="alert alert-info">
          <div class="d-flex align-items-center">
            <i data-feather="file-text" class="me-3 flex-shrink-0"></i>
            <div>
              <h6 class="mb-1">
                {% if study.patient_name and study.patient_id %} {{
                study.patient_name }} (ID: {{ study.patient_id }}) {% elif
                study.patient_name %} {{ study.patient_name }} {% elif
                study.patient_id %} Patient ID: {{ study.patient_id }} {% elif
                study.study_description %} {{ study.study_description }} {% else
                %} {{ study.original_filename }} {% endif %}
              </h6>
              <small class="text-muted">
                {% if study.study_description and (study.patient_name or
                study.patient_id) %} Study: {{ study.study_description }} • {%
                endif %} Study ID: {{ study.study_uid[:16] }}...
              </small>
            </div>
          </div>
        </div>

        {% if study.is_shared and study.share_token %}
        <!-- Already Shared - Show Links -->
        <div class="row">
          <div class="col-md-8">
            <h5 class="text-success">
              <i data-feather="check-circle" class="me-2"></i>
              Study is Currently Shared
            </h5>

            <!-- Share URL -->
            <div class="mb-3">
              <label class="form-label fw-semibold">Shareable Link</label>
              <div class="input-group">
                <input
                  type="text"
                  class="form-control"
                  id="share-url"
                  value="{{ share_url or url_for('shared_viewer', token=study.share_token, _external=True) }}"
                  readonly
                />
                <button
                  class="btn btn-outline-primary copy-to-clipboard"
                  data-target="share-url"
                  type="button"
                >
                  <i data-feather="copy" class="me-1"></i>
                  Copy
                </button>
              </div>
              <small class="text-muted"
                >Anyone with this link can view the anonymized study</small
              >
            </div>

            <!-- Expiration Info -->
            {% if study.share_expires %}
            <div class="mb-3">
              <label class="form-label fw-semibold">Link Expires</label>
              <div class="alert alert-warning">
                <i data-feather="clock" class="me-2"></i>
                {{ study.share_expires.strftime('%B %d, %Y at %H:%M') }} {% set
                time_left = (study.share_expires - now) %} {% if
                time_left.total_seconds() > 0 %}
                <small class="d-block text-muted">
                  ({{ time_left.days }} days remaining)
                </small>
                {% else %}
                <small class="d-block text-danger"> (Expired) </small>
                {% endif %}
              </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="d-grid gap-2 d-md-flex">
              <a
                href="{{ url_for('shared_viewer', token=study.share_token) }}"
                class="btn btn-primary"
                target="_blank"
              >
                <i data-feather="external-link" class="me-1"></i>
                Test Link
              </a>
              <form method="POST" class="d-inline">
                <input type="hidden" name="expiry_days" value="30" />
                <button type="submit" class="btn btn-outline-secondary">
                  <i data-feather="refresh-cw" class="me-1"></i>
                  Regenerate Link
                </button>
              </form>
            </div>
          </div>

          <!-- QR Code -->
          <div class="col-md-4">
            {% if qr_path %}
            <div class="qr-code-container">
              <h6 class="text-center mb-3">QR Code</h6>
              <img src="{{ qr_path }}" alt="QR Code" class="img-fluid" />
              <p class="text-center mt-2">
                <small class="text-muted">Scan for quick access</small>
              </p>
            </div>
            {% endif %}
          </div>
        </div>

        {% else %}
        <!-- Not Shared Yet - Generate Share Link -->
        <form method="POST">
          <div class="mb-4">
            <label for="expiry-days" class="form-label fw-semibold"
              >Link Expiration</label
            >
            <select class="form-select" name="expiry_days" id="expiry-days">
              <option value="1">1 Day</option>
              <option value="7">7 Days</option>
              <option value="30" selected>30 Days</option>
              <option value="90">90 Days</option>
              <option value="365">1 Year</option>
            </select>
            <small class="text-muted"
              >Choose how long the share link should remain active</small
            >
          </div>

          <div class="alert alert-warning">
            <i data-feather="shield" class="me-2"></i>
            <strong>Privacy Notice:</strong>
            This study has been automatically anonymized by removing patient
            identifiers. The shared link will only provide access to the
            anonymized medical imaging data.
          </div>

          <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a
              href="{{ url_for('viewer', study_uid=study.study_uid) }}"
              class="btn btn-secondary me-md-2"
            >
              <i data-feather="arrow-left" class="me-1"></i>
              Cancel
            </a>
            <button type="submit" class="btn btn-primary">
              <i data-feather="share-2" class="me-1"></i>
              Generate Share Link
            </button>
          </div>
        </form>
        {% endif %}
      </div>
    </div>

    <!-- Sharing Guidelines -->
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i data-feather="book-open" class="me-2"></i>
          Sharing Guidelines
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <h6 class="text-success">
              <i data-feather="check" class="me-1"></i>
              Safe to Share
            </h6>
            <ul class="list-unstyled">
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Medical conferences and presentations
              </li>
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Educational and training purposes
              </li>
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Research collaboration
              </li>
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Second opinion consultations
              </li>
            </ul>
          </div>
          <div class="col-md-6">
            <h6 class="text-warning">
              <i data-feather="alert-triangle" class="me-1"></i>
              Best Practices
            </h6>
            <ul class="list-unstyled">
              <li>
                <i
                  data-feather="shield"
                  class="text-primary me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Verify anonymization before sharing
              </li>
              <li>
                <i
                  data-feather="clock"
                  class="text-primary me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Set appropriate expiration dates
              </li>
              <li>
                <i
                  data-feather="lock"
                  class="text-primary me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Share only with authorized personnel
              </li>
              <li>
                <i
                  data-feather="eye"
                  class="text-primary me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Monitor access and usage
              </li>
            </ul>
          </div>
        </div>

        <div class="alert alert-info mt-3">
          <i data-feather="info" class="me-2"></i>
          <strong>Compliance Note:</strong>
          This platform automatically anonymizes DICOM files by removing patient
          identifiers to help ensure compliance with privacy regulations.
          However, users are responsible for verifying compliance with their
          specific organizational and regulatory requirements.
        </div>
      </div>
    </div>

    <!-- Access Statistics -->
    {% if study.is_shared %}
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i data-feather="bar-chart-2" class="me-2"></i>
          Access Statistics
        </h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-md-4">
            <div class="border rounded p-3">
              <h3 class="text-primary mb-1">{{ study.view_count }}</h3>
              <small class="text-muted">Total Views</small>
            </div>
          </div>
          <div class="col-md-4">
            <div class="border rounded p-3">
              <h3 class="text-primary mb-1">
                {% if study.last_viewed %} {{ (now - study.last_viewed).days }}
                {% else %} 0 {% endif %}
              </h3>
              <small class="text-muted">Days Since Last View</small>
            </div>
          </div>
          <div class="col-md-4">
            <div class="border rounded p-3">
              <h3 class="text-primary mb-1">
                {% if study.share_expires %} {{ (study.share_expires - now).days
                if (study.share_expires - now).total_seconds() > 0 else 0 }} {%
                else %} ∞ {% endif %}
              </h3>
              <small class="text-muted">Days Until Expiry</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>

{% if study.is_shared and study.share_token %}
<script>
  // Set current datetime for calculations
  const now = new Date();
</script>
{% endif %} {% endblock %}
