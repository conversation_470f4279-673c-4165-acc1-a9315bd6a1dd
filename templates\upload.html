{% extends "base.html" %} {% block title %}Upload DICOM - DICOM Platform{%
endblock %} {% block content %}
<div class="row justify-content-center">
  <div class="col-lg-8">
    <div class="card">
      <div class="card-header">
        <h3 class="mb-0">
          <i class="fas fa-upload me-2"></i>
          Upload DICOM Files
        </h3>
      </div>
      <div class="card-body">
        <form method="POST" enctype="multipart/form-data" id="upload-form">
          <div class="mb-4">
            <label class="form-label fw-semibold"
              >Select DICOM Files or Archives</label
            >
            <div class="upload-area" id="upload-area">
              <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
              </div>
              <h4>Drag & drop your DICOM files or archives here</h4>
              <p class="text-muted">or click to browse files</p>
              <p class="text-muted mb-2">
                <small><strong>Supported formats:</strong></small>
              </p>
              <div class="row text-center">
                <div class="col-md-4">
                  <div class="format-info">
                    <i class="fas fa-file-medical text-primary"></i>
                    <small class="d-block">DICOM Files</small>
                    <small class="text-muted">.dcm, .dicom, .dic</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="format-info">
                    <i class="fas fa-file-archive text-success"></i>
                    <small class="d-block">ZIP Archives</small>
                    <small class="text-muted">.zip</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="format-info">
                    <i class="fas fa-file-archive text-warning"></i>
                    <small class="d-block">RAR Archives</small>
                    <small class="text-muted">.rar</small>
                  </div>
                </div>
              </div>
              <p class="text-muted mt-2 mb-0">
                <small
                  ><i class="fas fa-info-circle"></i> Maximum file size: 500MB
                  per file</small
                >
              </p>
            </div>
            <input
              type="file"
              name="files"
              id="file-input"
              accept=".dcm,.dicom,.dic,.zip,.rar"
              style="display: none"
              multiple
              required
            />
          </div>

          <!-- Selected Files Display -->
          <div class="mb-3" id="selected-files" style="display: none">
            <label class="form-label fw-semibold">Selected Files:</label>
            <div class="selected-files-list" id="files-list">
              <!-- Files will be listed here -->
            </div>
          </div>

          <!-- Upload Progress -->
          <div class="progress mb-3" style="display: none" id="upload-progress">
            <div
              class="progress-bar progress-bar-striped progress-bar-animated"
              role="progressbar"
              style="width: 0%"
              aria-valuenow="0"
              aria-valuemin="0"
              aria-valuemax="100"
            >
              0%
            </div>
          </div>

          <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">
              <i data-feather="arrow-left" class="me-1"></i>
              Cancel
            </a>
            <button type="submit" class="btn btn-primary" id="submit-btn">
              <i class="fas fa-upload me-1"></i>
              Upload & Process Files
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Information Card -->
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i data-feather="info" class="me-2"></i>
          Important Information
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <h6 class="text-primary">
              <i data-feather="shield" class="me-1"></i>
              Privacy & Security
            </h6>
            <ul class="list-unstyled">
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Automatic patient data anonymization
              </li>
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Secure file storage
              </li>
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                HIPAA compliant processing
              </li>
            </ul>
          </div>
          <div class="col-md-6">
            <h6 class="text-primary">
              <i data-feather="file-text" class="me-1"></i>
              Supported Features
            </h6>
            <ul class="list-unstyled">
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                DICOM metadata extraction
              </li>
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Browser-based viewing
              </li>
              <li>
                <i
                  data-feather="check"
                  class="text-success me-1"
                  style="width: 16px; height: 16px"
                ></i>
                Shareable links & QR codes
              </li>
            </ul>
          </div>
        </div>

        <div class="alert alert-info mt-3">
          <i data-feather="alert-circle" class="me-2"></i>
          <strong>Processing Note:</strong>
          Upon upload, your DICOM file will be automatically anonymized by
          removing patient identifiers while preserving the medical imaging data
          and relevant metadata for clinical review.
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Multi-file upload handling
  const fileInput = document.getElementById("file-input");
  const uploadArea = document.getElementById("upload-area");
  const selectedFilesDiv = document.getElementById("selected-files");
  const filesList = document.getElementById("files-list");
  const submitBtn = document.getElementById("submit-btn");
  const progressBar = document.getElementById("upload-progress");

  // File input change handler
  fileInput.addEventListener("change", function (e) {
    displaySelectedFiles(e.target.files);
  });

  // Upload area click handler
  uploadArea.addEventListener("click", function () {
    fileInput.click();
  });

  // Drag and drop handlers
  uploadArea.addEventListener("dragover", function (e) {
    e.preventDefault();
    uploadArea.classList.add("dragover");
  });

  uploadArea.addEventListener("dragleave", function (e) {
    e.preventDefault();
    uploadArea.classList.remove("dragover");
  });

  uploadArea.addEventListener("drop", function (e) {
    e.preventDefault();
    uploadArea.classList.remove("dragover");

    const files = e.dataTransfer.files;
    fileInput.files = files;
    displaySelectedFiles(files);
  });

  // Display selected files
  function displaySelectedFiles(files) {
    if (files.length === 0) {
      selectedFilesDiv.style.display = "none";
      return;
    }

    selectedFilesDiv.style.display = "block";
    filesList.innerHTML = "";

    Array.from(files).forEach((file, index) => {
      const fileItem = document.createElement("div");
      fileItem.className =
        "file-item d-flex justify-content-between align-items-center p-2 mb-2 border rounded";

      const fileInfo = document.createElement("div");
      fileInfo.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas ${getFileIcon(file.name)} me-2"></i>
                <div>
                    <strong>${file.name}</strong>
                    <small class="text-muted d-block">${formatFileSize(
                      file.size
                    )}</small>
                </div>
            </div>
        `;

      const removeBtn = document.createElement("button");
      removeBtn.type = "button";
      removeBtn.className = "btn btn-sm btn-outline-danger";
      removeBtn.innerHTML = '<i class="fas fa-times"></i>';
      removeBtn.onclick = () => removeFile(index);

      fileItem.appendChild(fileInfo);
      fileItem.appendChild(removeBtn);
      filesList.appendChild(fileItem);
    });
  }

  // Get file icon based on extension
  function getFileIcon(filename) {
    const ext = filename.toLowerCase().split(".").pop();
    switch (ext) {
      case "dcm":
      case "dicom":
      case "dic":
        return "fa-file-medical text-primary";
      case "zip":
        return "fa-file-archive text-success";
      case "rar":
        return "fa-file-archive text-warning";
      default:
        return "fa-file text-secondary";
    }
  }

  // Format file size
  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // Remove file from selection
  function removeFile(index) {
    const dt = new DataTransfer();
    const files = Array.from(fileInput.files);

    files.forEach((file, i) => {
      if (i !== index) {
        dt.items.add(file);
      }
    });

    fileInput.files = dt.files;
    displaySelectedFiles(fileInput.files);
  }

  // Form submission handler
  document
    .getElementById("upload-form")
    .addEventListener("submit", function (e) {
      if (!fileInput.files || fileInput.files.length === 0) {
        e.preventDefault();
        alert("Please select at least one file to upload.");
        return;
      }

      // Show progress bar and disable submit button
      submitBtn.disabled = true;
      submitBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin me-1"></i> Processing Files...';
      progressBar.style.display = "block";

      // Simulate progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress > 90) progress = 90;

        const progressBarElement = progressBar.querySelector(".progress-bar");
        progressBarElement.style.width = progress + "%";
        progressBarElement.textContent = Math.round(progress) + "%";
        progressBarElement.setAttribute("aria-valuenow", progress);

        if (progress >= 90) {
          clearInterval(interval);
        }
      }, 300);
    });
</script>

<style>
  .format-info {
    padding: 1rem;
    margin: 0.5rem 0;
  }

  .format-info i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .upload-area.dragover {
    border-color: var(--primary);
    background-color: rgba(37, 99, 235, 0.1);
    transform: scale(1.02);
  }

  .file-item {
    background-color: var(--background-secondary);
    transition: all 0.3s ease;
  }

  .file-item:hover {
    background-color: var(--background-tertiary);
    transform: translateX(4px);
  }

  .selected-files-list {
    max-height: 300px;
    overflow-y: auto;
  }
</style>
{% endblock %}
