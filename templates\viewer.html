{% extends "base.html" %} {% block title %}DICOM Viewer - {{
study.study_description or 'Study' }}{% endblock %} {% block head %}
<!-- Cornerstone3D Dependencies -->
<script src="https://unpkg.com/@cornerstonejs/core@1.80.6/dist/umd/index.js"></script>
<script src="https://unpkg.com/@cornerstonejs/tools@1.80.6/dist/umd/index.js"></script>
<script src="https://unpkg.com/@cornerstonejs/streaming-image-volume-loader@1.80.6/dist/umd/index.js"></script>
<script src="https://unpkg.com/@cornerstonejs/dicom-image-loader@1.80.6/dist/dynamic-import/cornerstoneDICOMImageLoader.min.js"></script>
<script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>

<style>
  .medical-viewer-container {
    display: flex;
    height: 100vh;
    background: #1a1a1a;
    color: white;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  .viewer-sidebar {
    width: 250px;
    background: #2d2d2d;
    border-right: 1px solid #444;
    padding: 15px;
    overflow-y: auto;
  }

  .viewer-main {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .viewer-toolbar {
    background: #333;
    padding: 10px 15px;
    border-bottom: 1px solid #444;
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .viewer-content {
    flex: 1;
    position: relative;
    background: #000;
  }

  .viewport-container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .cornerstone-viewport {
    width: 100%;
    height: 100%;
  }

  .tool-button {
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
  }

  .tool-button:hover {
    background: #555;
    border-color: #777;
  }

  .tool-button.active {
    background: #0066cc;
    border-color: #0088ff;
  }

  .viewer-info {
    background: #2a2a2a;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 6px;
    border: 1px solid #444;
  }

  .info-item {
    margin-bottom: 8px;
    font-size: 13px;
  }

  .info-label {
    color: #aaa;
    font-weight: 500;
  }

  .info-value {
    color: #fff;
    margin-left: 8px;
  }

  .window-level-controls {
    background: #2a2a2a;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #444;
    margin-bottom: 15px;
  }

  .control-group {
    margin-bottom: 12px;
  }

  .control-label {
    display: block;
    color: #aaa;
    font-size: 12px;
    margin-bottom: 5px;
  }

  .control-input {
    width: 100%;
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 6px 8px;
    border-radius: 3px;
    font-size: 12px;
  }

  .preset-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    margin-top: 10px;
  }

  .preset-btn {
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    flex: 1;
    min-width: 60px;
  }

  .preset-btn:hover {
    background: #555;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .loading-content {
    text-align: center;
    color: white;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #333;
    border-top: 4px solid #0066cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .error-message {
    background: #d32f2f;
    color: white;
    padding: 15px;
    border-radius: 6px;
    margin: 20px;
  }
</style>
{% endblock %} {% block content %}
<div class="medical-viewer-container">
  <!-- Sidebar with Study Information and Controls -->
  <div class="viewer-sidebar">
    <div class="viewer-info">
      <h6 style="color: #0066cc; margin-bottom: 12px">Study Information</h6>
      <div class="info-item">
        <span class="info-label">Patient ID:</span>
        <span class="info-value">{{ study.patient_id or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Study Date:</span>
        <span class="info-value">{{ study.study_date or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Modality:</span>
        <span class="info-value">{{ study.modality or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Description:</span>
        <span class="info-value">{{ study.study_description or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Institution:</span>
        <span class="info-value">{{ study.institution_name or 'N/A' }}</span>
      </div>
    </div>

    <div class="window-level-controls">
      <h6 style="color: #0066cc; margin-bottom: 12px">Window/Level</h6>
      <div class="control-group">
        <label class="control-label">Window Width</label>
        <input
          type="range"
          id="windowWidth"
          class="control-input"
          min="1"
          max="4000"
          value="400"
          oninput="updateWindowLevel()"
        />
        <span id="windowWidthValue" style="color: #aaa; font-size: 11px"
          >400</span
        >
      </div>
      <div class="control-group">
        <label class="control-label">Window Center</label>
        <input
          type="range"
          id="windowCenter"
          class="control-input"
          min="-1000"
          max="3000"
          value="40"
          oninput="updateWindowLevel()"
        />
        <span id="windowCenterValue" style="color: #aaa; font-size: 11px"
          >40</span
        >
      </div>
      <div class="preset-buttons">
        <button class="preset-btn" onclick="applyPreset('lung')">Lung</button>
        <button class="preset-btn" onclick="applyPreset('bone')">Bone</button>
        <button class="preset-btn" onclick="applyPreset('brain')">Brain</button>
        <button class="preset-btn" onclick="applyPreset('abdomen')">
          Abdomen
        </button>
      </div>
    </div>

    <div
      style="
        background: #2a2a2a;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #444;
      "
    >
      <h6 style="color: #0066cc; margin-bottom: 12px">Navigation</h6>
      <div style="margin-bottom: 10px">
        <a
          href="{{ url_for('index') }}"
          class="tool-button"
          style="
            display: inline-block;
            text-decoration: none;
            width: 100%;
            text-align: center;
          "
        >
          <i class="fas fa-arrow-left"></i> Back to Studies
        </a>
      </div>
      {% if not is_shared %}
      <div style="margin-bottom: 10px">
        <a
          href="{{ url_for('share_study', study_uid=study.study_uid) }}"
          class="tool-button"
          style="
            display: inline-block;
            text-decoration: none;
            width: 100%;
            text-align: center;
          "
        >
          <i class="fas fa-share"></i> Share Study
        </a>
      </div>
      {% endif %}
    </div>
  </div>

  <!-- Main Viewer Area -->
  <div class="viewer-main">
    <!-- Toolbar -->
    <div class="viewer-toolbar">
      <button class="tool-button" id="panTool" onclick="setActiveTool('Pan')">
        <i class="fas fa-hand-paper"></i> Pan
      </button>
      <button class="tool-button" id="zoomTool" onclick="setActiveTool('Zoom')">
        <i class="fas fa-search-plus"></i> Zoom
      </button>
      <button
        class="tool-button"
        id="windowLevelTool"
        onclick="setActiveTool('WindowLevel')"
      >
        <i class="fas fa-adjust"></i> W/L
      </button>
      <button
        class="tool-button"
        id="lengthTool"
        onclick="setActiveTool('Length')"
      >
        <i class="fas fa-ruler"></i> Length
      </button>
      <button
        class="tool-button"
        id="angleTool"
        onclick="setActiveTool('Angle')"
      >
        <i class="fas fa-drafting-compass"></i> Angle
      </button>
      <button
        class="tool-button"
        id="rectangleTool"
        onclick="setActiveTool('RectangleROI')"
      >
        <i class="fas fa-square"></i> ROI
      </button>
      <button class="tool-button" onclick="resetView()">
        <i class="fas fa-undo"></i> Reset
      </button>
      <button class="tool-button" onclick="invertImage()">
        <i class="fas fa-adjust"></i> Invert
      </button>
    </div>

    <!-- Viewport -->
    <div class="viewer-content">
      <div id="viewport-container" class="viewport-container">
        <div
          id="cornerstone-viewport"
          class="cornerstone-viewport"
          data-study-uid="{{ study.study_uid }}"
        ></div>
        <div id="loading-overlay" class="loading-overlay">
          <div class="loading-content">
            <div class="spinner"></div>
            <p>Loading DICOM image...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %} {% block scripts %}
<script>
  // Medical DICOM Viewer using Cornerstone3D
  let viewport = null;
  let renderingEngine = null;
  let toolGroup = null;
  let currentImageId = null;

  // Initialize the viewer when DOM is loaded
  document.addEventListener("DOMContentLoaded", async function () {
    const viewportElement = document.getElementById("cornerstone-viewport");
    if (viewportElement) {
      const studyUid = viewportElement.getAttribute("data-study-uid");
      if (studyUid) {
        await initializeViewer(studyUid);
      }
    }
  });

  async function initializeViewer(studyUid) {
    try {
      console.log("Initializing medical DICOM viewer for study:", studyUid);

      // Check if Cornerstone3D is available
      if (typeof cornerstone === "undefined") {
        console.warn(
          "Cornerstone3D not available, falling back to simple viewer"
        );
        await initializeSimpleViewer(studyUid);
        return;
      }

      // Initialize Cornerstone3D
      await cornerstone.init();

      // Configure DICOM Image Loader
      if (typeof cornerstoneDICOMImageLoader !== "undefined") {
        cornerstoneDICOMImageLoader.external.cornerstone = cornerstone;
        cornerstoneDICOMImageLoader.external.dicomParser = dicomParser;
        cornerstoneDICOMImageLoader.configure({
          useWebWorkers: true,
          decodeConfig: {
            convertFloatPixelDataToInt: false,
          },
        });
      }

      // Create rendering engine
      renderingEngine = new cornerstone.RenderingEngine("medicalViewer");

      // Create viewport
      const viewportInput = {
        viewportId: "CT_AXIAL",
        type: cornerstone.Enums.ViewportType.STACK,
        element: document.getElementById("cornerstone-viewport"),
      };

      renderingEngine.enableElement(viewportInput);
      viewport = renderingEngine.getViewport("CT_AXIAL");

      // Initialize tools if available
      if (typeof cornerstoneTools !== "undefined") {
        initializeTools();
      }

      // Load the DICOM image
      await loadDicomImage(studyUid);

      // Hide loading overlay
      document.getElementById("loading-overlay").style.display = "none";
    } catch (error) {
      console.error("Error initializing medical viewer:", error);
      await initializeSimpleViewer(studyUid);
    }
  }

  async function initializeSimpleViewer(studyUid) {
    console.log("Initializing simple fallback viewer");
    try {
      const response = await fetch(`/api/dicom-image/${studyUid}`);
      if (!response.ok) {
        throw new Error(
          `Failed to load image: ${response.status} ${response.statusText}`
        );
      }

      const imageData = await response.json();

      const viewportElement = document.getElementById("cornerstone-viewport");
      viewportElement.innerHTML = `
            <img src="${imageData.image}"
                 style="width: 100%; height: 100%; object-fit: contain; background: #000;"
                 alt="DICOM Image">
        `;

      document.getElementById("loading-overlay").style.display = "none";
      console.log("Simple viewer loaded successfully");
    } catch (error) {
      console.error("Error in simple viewer:", error);
      showError(`Failed to load DICOM image: ${error.message}`);
    }
  }

  function initializeTools() {
    try {
      // Initialize Cornerstone Tools
      cornerstoneTools.init();

      // Create tool group
      toolGroup =
        cornerstoneTools.ToolGroupManager.createToolGroup("medicalToolGroup");

      // Add tools to the tool group
      toolGroup.addTool(cornerstoneTools.PanTool.toolName);
      toolGroup.addTool(cornerstoneTools.ZoomTool.toolName);
      toolGroup.addTool(cornerstoneTools.WindowLevelTool.toolName);
      toolGroup.addTool(cornerstoneTools.LengthTool.toolName);
      toolGroup.addTool(cornerstoneTools.AngleTool.toolName);
      toolGroup.addTool(cornerstoneTools.RectangleROITool.toolName);

      // Set tool modes
      toolGroup.setToolActive(cornerstoneTools.WindowLevelTool.toolName, {
        bindings: [
          { mouseButton: cornerstoneTools.Enums.MouseBindings.Primary },
        ],
      });

      toolGroup.setToolActive(cornerstoneTools.PanTool.toolName, {
        bindings: [
          { mouseButton: cornerstoneTools.Enums.MouseBindings.Auxiliary },
        ],
      });

      toolGroup.setToolActive(cornerstoneTools.ZoomTool.toolName, {
        bindings: [
          { mouseButton: cornerstoneTools.Enums.MouseBindings.Secondary },
        ],
      });

      // Add viewport to tool group
      toolGroup.addViewport("CT_AXIAL", "medicalViewer");

      console.log("Tools initialized successfully");
    } catch (error) {
      console.error("Error initializing tools:", error);
    }
  }

  async function loadDicomImage(studyUid) {
    try {
      // Use the DICOM data endpoint
      const imageId = `wadouri:/api/dicom-data/${studyUid}`;
      currentImageId = imageId;

      // Load and display the image
      const image = await cornerstone.imageLoader.loadAndCacheImage(imageId);
      await viewport.setStack([imageId]);

      console.log("DICOM image loaded successfully with Cornerstone3D");
    } catch (error) {
      console.error("Error loading DICOM image with Cornerstone3D:", error);
      throw error;
    }
  }

  // Tool activation functions
  function setActiveTool(toolName) {
    if (!toolGroup) {
      console.warn("Tools not available");
      return;
    }

    try {
      // Deactivate all tools first
      toolGroup.setToolPassive(cornerstoneTools.PanTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.ZoomTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.WindowLevelTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.LengthTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.AngleTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.RectangleROITool.toolName);

      // Remove active class from all buttons
      document
        .querySelectorAll(".tool-button")
        .forEach((btn) => btn.classList.remove("active"));

      // Activate selected tool
      const toolNameMap = {
        Pan: cornerstoneTools.PanTool.toolName,
        Zoom: cornerstoneTools.ZoomTool.toolName,
        WindowLevel: cornerstoneTools.WindowLevelTool.toolName,
        Length: cornerstoneTools.LengthTool.toolName,
        Angle: cornerstoneTools.AngleTool.toolName,
        RectangleROI: cornerstoneTools.RectangleROITool.toolName,
      };

      const actualToolName = toolNameMap[toolName];
      if (actualToolName) {
        toolGroup.setToolActive(actualToolName, {
          bindings: [
            { mouseButton: cornerstoneTools.Enums.MouseBindings.Primary },
          ],
        });

        // Add active class to button
        const buttonId = toolName.toLowerCase() + "Tool";
        const button = document.getElementById(buttonId);
        if (button) {
          button.classList.add("active");
        }
      }

      console.log(`Activated tool: ${toolName}`);
    } catch (error) {
      console.error("Error setting active tool:", error);
    }
  }

  // Window/Level functions
  function updateWindowLevel() {
    if (!viewport) return;

    try {
      const windowWidth = parseInt(
        document.getElementById("windowWidth").value
      );
      const windowCenter = parseInt(
        document.getElementById("windowCenter").value
      );

      document.getElementById("windowWidthValue").textContent = windowWidth;
      document.getElementById("windowCenterValue").textContent = windowCenter;

      const properties = viewport.getProperties();
      viewport.setProperties({
        ...properties,
        voiRange: {
          lower: windowCenter - windowWidth / 2,
          upper: windowCenter + windowWidth / 2,
        },
      });

      viewport.render();
    } catch (error) {
      console.error("Error updating window/level:", error);
    }
  }

  function applyPreset(presetName) {
    const presets = {
      lung: { width: 1500, center: -600 },
      bone: { width: 2000, center: 300 },
      brain: { width: 100, center: 50 },
      abdomen: { width: 350, center: 50 },
    };

    const preset = presets[presetName];
    if (preset && viewport) {
      document.getElementById("windowWidth").value = preset.width;
      document.getElementById("windowCenter").value = preset.center;
      updateWindowLevel();
    }
  }

  // Utility functions
  function resetView() {
    if (viewport) {
      viewport.resetCamera();
      viewport.render();
    }
  }

  function invertImage() {
    if (viewport) {
      try {
        const properties = viewport.getProperties();
        const currentInvert = properties.invert || false;
        viewport.setProperties({
          ...properties,
          invert: !currentInvert,
        });
        viewport.render();
      } catch (error) {
        console.error("Error inverting image:", error);
      }
    }
  }

  function showError(message) {
    const loadingOverlay = document.getElementById("loading-overlay");
    loadingOverlay.innerHTML = `
        <div class="error-message">
            <h6>Viewer Error</h6>
            <p>${message}</p>
            <small>Please try refreshing the page or contact support.</small>
        </div>
    `;
  }
</script>

{% endblock %}
