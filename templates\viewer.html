{% extends "base.html" %} {% block title %}DICOM Viewer - {{
study.study_description or 'Study' }}{% endblock %} {% block head %}
<!-- Enhanced Medical Viewer Dependencies -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>

<style>
  .medical-viewer-container {
    display: flex;
    height: 100vh;
    background: #1a1a1a;
    color: white;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  .viewer-sidebar {
    width: 250px;
    background: #2d2d2d;
    border-right: 1px solid #444;
    padding: 15px;
    overflow-y: auto;
  }

  .viewer-main {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .viewer-toolbar {
    background: #333;
    padding: 10px 15px;
    border-bottom: 1px solid #444;
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .viewer-content {
    flex: 1;
    position: relative;
    background: #000;
  }

  .viewport-container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .cornerstone-viewport {
    width: 100%;
    height: 100%;
  }

  .tool-button {
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
  }

  .tool-button:hover {
    background: #555;
    border-color: #777;
  }

  .tool-button.active {
    background: #0066cc;
    border-color: #0088ff;
  }

  .viewer-info {
    background: #2a2a2a;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 6px;
    border: 1px solid #444;
  }

  .info-item {
    margin-bottom: 8px;
    font-size: 13px;
  }

  .info-label {
    color: #aaa;
    font-weight: 500;
  }

  .info-value {
    color: #fff;
    margin-left: 8px;
  }

  .window-level-controls {
    background: #2a2a2a;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #444;
    margin-bottom: 15px;
  }

  .control-group {
    margin-bottom: 12px;
  }

  .control-label {
    display: block;
    color: #aaa;
    font-size: 12px;
    margin-bottom: 5px;
  }

  .control-input {
    width: 100%;
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 6px 8px;
    border-radius: 3px;
    font-size: 12px;
  }

  .preset-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    margin-top: 10px;
  }

  .preset-btn {
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    flex: 1;
    min-width: 60px;
  }

  .preset-btn:hover {
    background: #555;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .loading-content {
    text-align: center;
    color: white;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #333;
    border-top: 4px solid #0066cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .error-message {
    background: #d32f2f;
    color: white;
    padding: 15px;
    border-radius: 6px;
    margin: 20px;
  }
</style>
{% endblock %} {% block content %}
<div class="medical-viewer-container">
  <!-- Sidebar with Study Information and Controls -->
  <div class="viewer-sidebar">
    <div class="viewer-info">
      <h6 style="color: #0066cc; margin-bottom: 12px">Study Information</h6>
      <div class="info-item">
        <span class="info-label">Patient ID:</span>
        <span class="info-value">{{ study.patient_id or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Study Date:</span>
        <span class="info-value">{{ study.study_date or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Modality:</span>
        <span class="info-value">{{ study.modality or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Description:</span>
        <span class="info-value">{{ study.study_description or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Institution:</span>
        <span class="info-value">{{ study.institution_name or 'N/A' }}</span>
      </div>
    </div>

    <div class="window-level-controls">
      <h6 style="color: #0066cc; margin-bottom: 12px">Window/Level</h6>
      <div class="control-group">
        <label class="control-label">Window Width</label>
        <input
          type="range"
          id="windowWidth"
          class="control-input"
          min="1"
          max="4000"
          value="400"
          oninput="updateWindowLevel()"
        />
        <span id="windowWidthValue" style="color: #aaa; font-size: 11px"
          >400</span
        >
      </div>
      <div class="control-group">
        <label class="control-label">Window Center</label>
        <input
          type="range"
          id="windowCenter"
          class="control-input"
          min="-1000"
          max="3000"
          value="40"
          oninput="updateWindowLevel()"
        />
        <span id="windowCenterValue" style="color: #aaa; font-size: 11px"
          >40</span
        >
      </div>
      <div class="preset-buttons">
        <button class="preset-btn" onclick="applyPreset('lung')">Lung</button>
        <button class="preset-btn" onclick="applyPreset('bone')">Bone</button>
        <button class="preset-btn" onclick="applyPreset('brain')">Brain</button>
        <button class="preset-btn" onclick="applyPreset('abdomen')">
          Abdomen
        </button>
      </div>
    </div>

    <div
      style="
        background: #2a2a2a;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #444;
      "
    >
      <h6 style="color: #0066cc; margin-bottom: 12px">Navigation</h6>
      <div style="margin-bottom: 10px">
        <a
          href="{{ url_for('index') }}"
          class="tool-button"
          style="
            display: inline-block;
            text-decoration: none;
            width: 100%;
            text-align: center;
          "
        >
          <i class="fas fa-arrow-left"></i> Back to Studies
        </a>
      </div>
      {% if not is_shared %}
      <div style="margin-bottom: 10px">
        <a
          href="{{ url_for('share_study', study_uid=study.study_uid) }}"
          class="tool-button"
          style="
            display: inline-block;
            text-decoration: none;
            width: 100%;
            text-align: center;
          "
        >
          <i class="fas fa-share"></i> Share Study
        </a>
      </div>
      {% endif %}
    </div>
  </div>

  <!-- Main Viewer Area -->
  <div class="viewer-main">
    <!-- Toolbar -->
    <div class="viewer-toolbar">
      <button class="tool-button" id="panTool" onclick="setActiveTool('Pan')">
        <i class="fas fa-hand-paper"></i> Pan
      </button>
      <button class="tool-button" id="zoomTool" onclick="setActiveTool('Zoom')">
        <i class="fas fa-search-plus"></i> Zoom
      </button>
      <button
        class="tool-button"
        id="windowLevelTool"
        onclick="setActiveTool('WindowLevel')"
      >
        <i class="fas fa-adjust"></i> W/L
      </button>
      <button
        class="tool-button"
        id="lengthTool"
        onclick="setActiveTool('Length')"
      >
        <i class="fas fa-ruler"></i> Length
      </button>
      <button
        class="tool-button"
        id="angleTool"
        onclick="setActiveTool('Angle')"
      >
        <i class="fas fa-drafting-compass"></i> Angle
      </button>
      <button
        class="tool-button"
        id="rectangleTool"
        onclick="setActiveTool('RectangleROI')"
      >
        <i class="fas fa-square"></i> ROI
      </button>
      <button class="tool-button" onclick="resetView()">
        <i class="fas fa-undo"></i> Reset
      </button>
      <button class="tool-button" onclick="invertImage()">
        <i class="fas fa-adjust"></i> Invert
      </button>
    </div>

    <!-- Viewport -->
    <div class="viewer-content">
      <div id="viewport-container" class="viewport-container">
        <div
          id="image-container"
          style="position: relative; width: 100%; height: 100%"
        >
          <img
            id="dicom-image"
            style="
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: contain;
              background: #000;
            "
          />
          <canvas
            id="annotation-canvas"
            style="
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              pointer-events: none;
            "
          ></canvas>
          <canvas
            id="interaction-canvas"
            style="
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              cursor: crosshair;
            "
            data-study-uid="{{ study.study_uid }}"
          ></canvas>
        </div>
        <div id="loading-overlay" class="loading-overlay">
          <div class="loading-content">
            <div class="spinner"></div>
            <p>Loading DICOM image...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %} {% block scripts %}
<script>
  // Enhanced Medical DICOM Viewer with Interactive Tools
  class MedicalDicomViewer {
    constructor() {
      this.currentTool = 'WindowLevel';
      this.isMouseDown = false;
      this.lastMousePos = { x: 0, y: 0 };
      this.imageTransform = {
        scale: 1,
        translateX: 0,
        translateY: 0,
        windowWidth: 400,
        windowCenter: 40,
        brightness: 0,
        contrast: 100,
        invert: false
      };
      this.measurements = [];
      this.currentMeasurement = null;

      this.imageElement = null;
      this.canvas = null;
      this.ctx = null;
      this.annotationCanvas = null;
      this.annotationCtx = null;
    }

    async init(studyUid) {
      try {
        console.log('Initializing Enhanced Medical DICOM Viewer for study:', studyUid);

        // Get DOM elements
        this.imageElement = document.getElementById('dicom-image');
        this.canvas = document.getElementById('interaction-canvas');
        this.annotationCanvas = document.getElementById('annotation-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.annotationCtx = this.annotationCanvas.getContext('2d');

        // Load DICOM image
        await this.loadDicomImage(studyUid);

        // Setup canvas and event listeners
        this.setupCanvas();
        this.setupEventListeners();
        this.setupTouchGestures();

        // Initialize window/level controls
        this.updateWindowLevelControls();

        // Hide loading overlay
        document.getElementById('loading-overlay').style.display = 'none';

        console.log('Enhanced Medical DICOM Viewer initialized successfully');

      } catch (error) {
        console.error('Error initializing enhanced viewer:', error);
        this.showError(`Failed to initialize viewer: ${error.message}`);
      }
    }

    async loadDicomImage(studyUid) {
      try {
        const response = await fetch(`/api/dicom-image/${studyUid}`);
        if (!response.ok) {
          throw new Error(`Failed to load image: ${response.status} ${response.statusText}`);
        }

        const imageData = await response.json();

        return new Promise((resolve, reject) => {
          this.imageElement.onload = () => {
            console.log('DICOM image loaded successfully');
            this.applyImageTransform();
            resolve();
          };
          this.imageElement.onerror = () => {
            reject(new Error('Failed to load image'));
          };
          this.imageElement.src = imageData.image;
        });

      } catch (error) {
        console.error('Error loading DICOM image:', error);
        throw error;
      }
    }

    setupCanvas() {
      const container = document.getElementById('image-container');
      const rect = container.getBoundingClientRect();

      // Set canvas size to match container
      this.canvas.width = rect.width;
      this.canvas.height = rect.height;
      this.annotationCanvas.width = rect.width;
      this.annotationCanvas.height = rect.height;

      // Handle window resize
      window.addEventListener('resize', () => {
        setTimeout(() => this.setupCanvas(), 100);
      });
    }

    setupEventListeners() {
      // Mouse events
      this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
      this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
      this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
      this.canvas.addEventListener('wheel', (e) => this.handleWheel(e));
      this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());

      // Prevent default drag behavior
      this.canvas.addEventListener('dragstart', (e) => e.preventDefault());
    }

    setupTouchGestures() {
      if (typeof Hammer !== 'undefined') {
        const hammer = new Hammer(this.canvas);
        hammer.get('pinch').set({ enable: true });
        hammer.get('pan').set({ direction: Hammer.DIRECTION_ALL });

        hammer.on('pinch', (e) => {
          this.imageTransform.scale *= e.scale;
          this.imageTransform.scale = Math.max(0.1, Math.min(10, this.imageTransform.scale));
          this.applyImageTransform();
        });

        hammer.on('pan', (e) => {
          if (this.currentTool === 'Pan') {
            this.imageTransform.translateX += e.deltaX * 0.5;
            this.imageTransform.translateY += e.deltaY * 0.5;
            this.applyImageTransform();
          }
        });
      }
    }

    handleMouseDown(e) {
      this.isMouseDown = true;
      const rect = this.canvas.getBoundingClientRect();
      this.lastMousePos = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };

      if (this.currentTool === 'Length' || this.currentTool === 'Angle') {
        this.startMeasurement(this.lastMousePos);
      }
    }

    handleMouseMove(e) {
      if (!this.isMouseDown) return;

      const rect = this.canvas.getBoundingClientRect();
      const currentPos = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };

      const deltaX = currentPos.x - this.lastMousePos.x;
      const deltaY = currentPos.y - this.lastMousePos.y;

      switch (this.currentTool) {
        case 'Pan':
          this.imageTransform.translateX += deltaX;
          this.imageTransform.translateY += deltaY;
          this.applyImageTransform();
          break;

        case 'WindowLevel':
          // Window width (horizontal movement)
          this.imageTransform.windowWidth += deltaX * 2;
          this.imageTransform.windowWidth = Math.max(1, Math.min(4000, this.imageTransform.windowWidth));

          // Window center (vertical movement)
          this.imageTransform.windowCenter -= deltaY * 2;
          this.imageTransform.windowCenter = Math.max(-1000, Math.min(3000, this.imageTransform.windowCenter));

          this.applyImageTransform();
          this.updateWindowLevelControls();
          break;

        case 'Length':
        case 'Angle':
          if (this.currentMeasurement) {
            this.updateMeasurement(currentPos);
          }
          break;
      }

      this.lastMousePos = currentPos;
    }

    handleMouseUp(e) {
      this.isMouseDown = false;

      if (this.currentTool === 'Length' || this.currentTool === 'Angle') {
        this.finishMeasurement();
      }
    }

    handleWheel(e) {
      e.preventDefault();

      if (this.currentTool === 'Zoom' || e.ctrlKey) {
        const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
        this.imageTransform.scale *= scaleFactor;
        this.imageTransform.scale = Math.max(0.1, Math.min(10, this.imageTransform.scale));
        this.applyImageTransform();
      }
    }

    applyImageTransform() {
      if (!this.imageElement) return;

      const { scale, translateX, translateY, windowWidth, windowCenter, brightness, contrast, invert } = this.imageTransform;

      // Apply CSS transforms
      this.imageElement.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;

      // Apply window/level as CSS filters
      const brightnessValue = 100 + brightness + (windowCenter - 40) * 0.5;
      const contrastValue = contrast + (windowWidth - 400) * 0.1;
      const invertValue = invert ? 1 : 0;

      this.imageElement.style.filter = `brightness(${brightnessValue}%) contrast(${contrastValue}%) invert(${invertValue})`;
    }

    updateWindowLevelControls() {
      const windowWidthSlider = document.getElementById('windowWidth');
      const windowCenterSlider = document.getElementById('windowCenter');
      const windowWidthValue = document.getElementById('windowWidthValue');
      const windowCenterValue = document.getElementById('windowCenterValue');

      if (windowWidthSlider) {
        windowWidthSlider.value = this.imageTransform.windowWidth;
        windowWidthValue.textContent = Math.round(this.imageTransform.windowWidth);
      }

      if (windowCenterSlider) {
        windowCenterSlider.value = this.imageTransform.windowCenter;
        windowCenterValue.textContent = Math.round(this.imageTransform.windowCenter);
      }
    }

    startMeasurement(startPos) {
      this.currentMeasurement = {
        type: this.currentTool,
        points: [startPos],
        id: Date.now()
      };
    }

    updateMeasurement(currentPos) {
      if (!this.currentMeasurement) return;

      if (this.currentMeasurement.type === 'Length') {
        this.currentMeasurement.points = [this.currentMeasurement.points[0], currentPos];
      } else if (this.currentMeasurement.type === 'Angle') {
        if (this.currentMeasurement.points.length === 1) {
          this.currentMeasurement.points.push(currentPos);
        } else if (this.currentMeasurement.points.length === 2) {
          this.currentMeasurement.points[2] = currentPos;
        }
      }

      this.drawMeasurements();
    }

    finishMeasurement() {
      if (this.currentMeasurement && this.currentMeasurement.points.length >= 2) {
        this.measurements.push({ ...this.currentMeasurement });
        console.log('Measurement added:', this.currentMeasurement);
      }
      this.currentMeasurement = null;
      this.drawMeasurements();
    }

    drawMeasurements() {
      if (!this.annotationCtx) return;

      // Clear previous annotations
      this.annotationCtx.clearRect(0, 0, this.annotationCanvas.width, this.annotationCanvas.height);

      // Draw all measurements
      [...this.measurements, this.currentMeasurement].filter(Boolean).forEach(measurement => {
        this.drawSingleMeasurement(measurement);
      });
    }

    drawSingleMeasurement(measurement) {
      const ctx = this.annotationCtx;
      ctx.strokeStyle = '#00ff00';
      ctx.fillStyle = '#00ff00';
      ctx.lineWidth = 2;
      ctx.font = '14px Arial';

      if (measurement.type === 'Length' && measurement.points.length >= 2) {
        const [start, end] = measurement.points;

        // Draw line
        ctx.beginPath();
        ctx.moveTo(start.x, start.y);
        ctx.lineTo(end.x, end.y);
        ctx.stroke();

        // Draw endpoints
        ctx.fillRect(start.x - 3, start.y - 3, 6, 6);
        ctx.fillRect(end.x - 3, end.y - 3, 6, 6);

        // Calculate and display length
        const length = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2));
        const midX = (start.x + end.x) / 2;
        const midY = (start.y + end.y) / 2;

        ctx.fillText(`${length.toFixed(1)}px`, midX + 5, midY - 5);
      }

      if (measurement.type === 'Angle' && measurement.points.length >= 3) {
        const [p1, p2, p3] = measurement.points;

        // Draw lines
        ctx.beginPath();
        ctx.moveTo(p1.x, p1.y);
        ctx.lineTo(p2.x, p2.y);
        ctx.lineTo(p3.x, p3.y);
        ctx.stroke();

        // Draw points
        [p1, p2, p3].forEach(point => {
          ctx.fillRect(point.x - 3, point.y - 3, 6, 6);
        });

        // Calculate and display angle
        const angle1 = Math.atan2(p1.y - p2.y, p1.x - p2.x);
        const angle2 = Math.atan2(p3.y - p2.y, p3.x - p2.x);
        let angle = Math.abs(angle1 - angle2) * 180 / Math.PI;
        if (angle > 180) angle = 360 - angle;

        ctx.fillText(`${angle.toFixed(1)}°`, p2.x + 10, p2.y - 10);
      }
    }

    showError(message) {
      const loadingOverlay = document.getElementById('loading-overlay');
      loadingOverlay.innerHTML = `
        <div class="error-message">
          <h6>Viewer Error</h6>
          <p>${message}</p>
          <small>Please try refreshing the page or contact support.</small>
        </div>
      `;
    }
  }

  // Global viewer instance
  let medicalViewer = null;

  // Initialize viewer when DOM is loaded
  document.addEventListener('DOMContentLoaded', async function() {
    const canvas = document.getElementById('interaction-canvas');
    if (canvas) {
      const studyUid = canvas.getAttribute('data-study-uid');
      if (studyUid) {
        medicalViewer = new MedicalDicomViewer();
        await medicalViewer.init(studyUid);
      }
    }
  });

  // Global functions for toolbar buttons
  function setActiveTool(toolName) {
    if (!medicalViewer) return;

    medicalViewer.currentTool = toolName;

    // Update button states
    document.querySelectorAll('.tool-button').forEach(btn => btn.classList.remove('active'));
    const buttonId = toolName.toLowerCase() + 'Tool';
    const button = document.getElementById(buttonId);
    if (button) {
      button.classList.add('active');
    }

    // Update cursor
    const canvas = document.getElementById('interaction-canvas');
    switch (toolName) {
      case 'Pan':
        canvas.style.cursor = 'move';
        break;
      case 'Zoom':
        canvas.style.cursor = 'zoom-in';
        break;
      case 'WindowLevel':
        canvas.style.cursor = 'crosshair';
        break;
      case 'Length':
      case 'Angle':
      case 'RectangleROI':
        canvas.style.cursor = 'crosshair';
        break;
      default:
        canvas.style.cursor = 'default';
    }

    console.log(`Activated tool: ${toolName}`);
  }

  // Window/Level functions
  function updateWindowLevel() {
    if (!medicalViewer) return;

    const windowWidth = parseInt(document.getElementById('windowWidth').value);
    const windowCenter = parseInt(document.getElementById('windowCenter').value);

    medicalViewer.imageTransform.windowWidth = windowWidth;
    medicalViewer.imageTransform.windowCenter = windowCenter;

    medicalViewer.applyImageTransform();
    medicalViewer.updateWindowLevelControls();
  }

  function applyPreset(presetName) {
    if (!medicalViewer) return;

    const presets = {
      lung: { width: 1500, center: -600 },
      bone: { width: 2000, center: 300 },
      brain: { width: 100, center: 50 },
      abdomen: { width: 350, center: 50 }
    };

    const preset = presets[presetName];
    if (preset) {
      medicalViewer.imageTransform.windowWidth = preset.width;
      medicalViewer.imageTransform.windowCenter = preset.center;
      medicalViewer.applyImageTransform();
      medicalViewer.updateWindowLevelControls();
    }
  }

  // Utility functions
  function resetView() {
    if (!medicalViewer) return;

    medicalViewer.imageTransform = {
      scale: 1,
      translateX: 0,
      translateY: 0,
      windowWidth: 400,
      windowCenter: 40,
      brightness: 0,
      contrast: 100,
      invert: false
    };

    medicalViewer.applyImageTransform();
    medicalViewer.updateWindowLevelControls();

    // Clear measurements
    medicalViewer.measurements = [];
    medicalViewer.drawMeasurements();
  }

  function invertImage() {
    if (!medicalViewer) return;

    medicalViewer.imageTransform.invert = !medicalViewer.imageTransform.invert;
    medicalViewer.applyImageTransform();
  }

  // Set default tool
  setTimeout(() => {
    setActiveTool('WindowLevel');
  }, 1000);
        cornerstoneTools.ToolGroupManager.createToolGroup("medicalToolGroup");

      // Add tools to the tool group
      toolGroup.addTool(cornerstoneTools.PanTool.toolName);
      toolGroup.addTool(cornerstoneTools.ZoomTool.toolName);
      toolGroup.addTool(cornerstoneTools.WindowLevelTool.toolName);
      toolGroup.addTool(cornerstoneTools.LengthTool.toolName);
      toolGroup.addTool(cornerstoneTools.AngleTool.toolName);
      toolGroup.addTool(cornerstoneTools.RectangleROITool.toolName);

      // Set tool modes
      toolGroup.setToolActive(cornerstoneTools.WindowLevelTool.toolName, {
        bindings: [
          { mouseButton: cornerstoneTools.Enums.MouseBindings.Primary },
        ],
      });

      toolGroup.setToolActive(cornerstoneTools.PanTool.toolName, {
        bindings: [
          { mouseButton: cornerstoneTools.Enums.MouseBindings.Auxiliary },
        ],
      });

      toolGroup.setToolActive(cornerstoneTools.ZoomTool.toolName, {
        bindings: [
          { mouseButton: cornerstoneTools.Enums.MouseBindings.Secondary },
        ],
      });

      // Add viewport to tool group
      toolGroup.addViewport("CT_AXIAL", "medicalViewer");

      console.log("Tools initialized successfully");
    } catch (error) {
      console.error("Error initializing tools:", error);
    }
  }

  async function loadDicomImage(studyUid) {
    try {
      // Use the DICOM data endpoint
      const imageId = `wadouri:/api/dicom-data/${studyUid}`;
      currentImageId = imageId;

      // Load and display the image
      const image = await cornerstone.imageLoader.loadAndCacheImage(imageId);
      await viewport.setStack([imageId]);

      console.log("DICOM image loaded successfully with Cornerstone3D");
    } catch (error) {
      console.error("Error loading DICOM image with Cornerstone3D:", error);
      throw error;
    }
  }

  // Tool activation functions
  function setActiveTool(toolName) {
    if (!toolGroup) {
      console.warn("Tools not available");
      return;
    }

    try {
      // Deactivate all tools first
      toolGroup.setToolPassive(cornerstoneTools.PanTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.ZoomTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.WindowLevelTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.LengthTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.AngleTool.toolName);
      toolGroup.setToolPassive(cornerstoneTools.RectangleROITool.toolName);

      // Remove active class from all buttons
      document
        .querySelectorAll(".tool-button")
        .forEach((btn) => btn.classList.remove("active"));

      // Activate selected tool
      const toolNameMap = {
        Pan: cornerstoneTools.PanTool.toolName,
        Zoom: cornerstoneTools.ZoomTool.toolName,
        WindowLevel: cornerstoneTools.WindowLevelTool.toolName,
        Length: cornerstoneTools.LengthTool.toolName,
        Angle: cornerstoneTools.AngleTool.toolName,
        RectangleROI: cornerstoneTools.RectangleROITool.toolName,
      };

      const actualToolName = toolNameMap[toolName];
      if (actualToolName) {
        toolGroup.setToolActive(actualToolName, {
          bindings: [
            { mouseButton: cornerstoneTools.Enums.MouseBindings.Primary },
          ],
        });

        // Add active class to button
        const buttonId = toolName.toLowerCase() + "Tool";
        const button = document.getElementById(buttonId);
        if (button) {
          button.classList.add("active");
        }
      }

      console.log(`Activated tool: ${toolName}`);
    } catch (error) {
      console.error("Error setting active tool:", error);
    }
  }

  // Window/Level functions
  function updateWindowLevel() {
    if (!viewport) return;

    try {
      const windowWidth = parseInt(
        document.getElementById("windowWidth").value
      );
      const windowCenter = parseInt(
        document.getElementById("windowCenter").value
      );

      document.getElementById("windowWidthValue").textContent = windowWidth;
      document.getElementById("windowCenterValue").textContent = windowCenter;

      const properties = viewport.getProperties();
      viewport.setProperties({
        ...properties,
        voiRange: {
          lower: windowCenter - windowWidth / 2,
          upper: windowCenter + windowWidth / 2,
        },
      });

      viewport.render();
    } catch (error) {
      console.error("Error updating window/level:", error);
    }
  }

  function applyPreset(presetName) {
    const presets = {
      lung: { width: 1500, center: -600 },
      bone: { width: 2000, center: 300 },
      brain: { width: 100, center: 50 },
      abdomen: { width: 350, center: 50 },
    };

    const preset = presets[presetName];
    if (preset && viewport) {
      document.getElementById("windowWidth").value = preset.width;
      document.getElementById("windowCenter").value = preset.center;
      updateWindowLevel();
    }
  }

  // Utility functions
  function resetView() {
    if (viewport) {
      viewport.resetCamera();
      viewport.render();
    }
  }

  function invertImage() {
    if (viewport) {
      try {
        const properties = viewport.getProperties();
        const currentInvert = properties.invert || false;
        viewport.setProperties({
          ...properties,
          invert: !currentInvert,
        });
        viewport.render();
      } catch (error) {
        console.error("Error inverting image:", error);
      }
    }
  }

  function showError(message) {
    const loadingOverlay = document.getElementById("loading-overlay");
    loadingOverlay.innerHTML = `
        <div class="error-message">
            <h6>Viewer Error</h6>
            <p>${message}</p>
            <small>Please try refreshing the page or contact support.</small>
        </div>
    `;
  }
</script>

{% endblock %}
