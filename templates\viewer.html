{% extends "base.html" %} {% block title %}DICOM Viewer - {{
study.study_description or 'Study' }}{% endblock %} {% block head %}
<!-- Enhanced Medical Viewer Dependencies -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>

<style>
  .medical-viewer-container {
    display: flex;
    height: 100vh;
    background: #1a1a1a;
    color: white;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  .viewer-sidebar {
    width: 250px;
    background: #2d2d2d;
    border-right: 1px solid #444;
    padding: 15px;
    overflow-y: auto;
  }

  .viewer-main {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .viewer-toolbar {
    background: #333;
    padding: 10px 15px;
    border-bottom: 1px solid #444;
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .viewer-content {
    flex: 1;
    position: relative;
    background: #000;
  }

  .viewport-container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .cornerstone-viewport {
    width: 100%;
    height: 100%;
  }

  .tool-button {
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
  }

  .tool-button:hover {
    background: #555;
    border-color: #777;
  }

  .tool-button.active {
    background: #0066cc;
    border-color: #0088ff;
  }

  .viewer-info {
    background: #2a2a2a;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 6px;
    border: 1px solid #444;
  }

  .info-item {
    margin-bottom: 8px;
    font-size: 13px;
  }

  .info-label {
    color: #aaa;
    font-weight: 500;
  }

  .info-value {
    color: #fff;
    margin-left: 8px;
  }

  .window-level-controls {
    background: #2a2a2a;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #444;
    margin-bottom: 15px;
  }

  .control-group {
    margin-bottom: 12px;
  }

  .control-label {
    display: block;
    color: #aaa;
    font-size: 12px;
    margin-bottom: 5px;
  }

  .control-input {
    width: 100%;
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 6px 8px;
    border-radius: 3px;
    font-size: 12px;
  }

  .preset-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    margin-top: 10px;
  }

  .preset-btn {
    background: #444;
    border: 1px solid #666;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    flex: 1;
    min-width: 60px;
  }

  .preset-btn:hover {
    background: #555;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .loading-content {
    text-align: center;
    color: white;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #333;
    border-top: 4px solid #0066cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .error-message {
    background: #d32f2f;
    color: white;
    padding: 15px;
    border-radius: 6px;
    margin: 20px;
  }
</style>
{% endblock %} {% block content %}
<div class="medical-viewer-container">
  <!-- Sidebar with Study Information and Controls -->
  <div class="viewer-sidebar">
    <div class="viewer-info">
      <h6 style="color: #0066cc; margin-bottom: 12px">Patient Information</h6>
      <div class="info-item">
        <span class="info-label">Patient Name:</span>
        <span class="info-value">{{ study.patient_name or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Patient ID:</span>
        <span class="info-value">{{ study.patient_id or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Birth Date:</span>
        <span class="info-value">{{ study.patient_birth_date or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Sex:</span>
        <span class="info-value">{{ study.patient_sex or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Age:</span>
        <span class="info-value">{{ study.patient_age or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Weight:</span>
        <span class="info-value">{{ study.patient_weight or 'N/A' }}</span>
      </div>

      <h6 style="color: #0066cc; margin-bottom: 12px; margin-top: 20px">
        Study Information
      </h6>
      <div class="info-item">
        <span class="info-label">Study Date:</span>
        <span class="info-value">{{ study.study_date or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Study Time:</span>
        <span class="info-value">{{ study.study_time or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Modality:</span>
        <span class="info-value">{{ study.modality or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Description:</span>
        <span class="info-value">{{ study.study_description or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Accession Number:</span>
        <span class="info-value">{{ study.accession_number or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Study ID:</span>
        <span class="info-value">{{ study.study_id_dicom or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Institution:</span>
        <span class="info-value">{{ study.institution_name or 'N/A' }}</span>
      </div>

      <h6 style="color: #0066cc; margin-bottom: 12px; margin-top: 20px">
        Physicians
      </h6>
      <div class="info-item">
        <span class="info-label">Referring Physician:</span>
        <span class="info-value">{{ study.referring_physician or 'N/A' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Performing Physician:</span>
        <span class="info-value"
          >{{ study.performing_physician or 'N/A' }}</span
        >
      </div>
      <div class="info-item">
        <span class="info-label">Requesting Physician:</span>
        <span class="info-value"
          >{{ study.requesting_physician or 'N/A' }}</span
        >
      </div>
    </div>

    <div class="window-level-controls">
      <h6 style="color: #0066cc; margin-bottom: 12px">Window/Level</h6>
      <div class="control-group">
        <label class="control-label">Window Width</label>
        <input
          type="range"
          id="windowWidth"
          class="control-input"
          min="1"
          max="4000"
          value="400"
          oninput="updateWindowLevel()"
        />
        <span id="windowWidthValue" style="color: #aaa; font-size: 11px"
          >400</span
        >
      </div>
      <div class="control-group">
        <label class="control-label">Window Center</label>
        <input
          type="range"
          id="windowCenter"
          class="control-input"
          min="-1000"
          max="3000"
          value="40"
          oninput="updateWindowLevel()"
        />
        <span id="windowCenterValue" style="color: #aaa; font-size: 11px"
          >40</span
        >
      </div>
      <div class="preset-buttons">
        <button class="preset-btn" onclick="applyPreset('lung')">Lung</button>
        <button class="preset-btn" onclick="applyPreset('bone')">Bone</button>
        <button class="preset-btn" onclick="applyPreset('brain')">Brain</button>
        <button class="preset-btn" onclick="applyPreset('abdomen')">
          Abdomen
        </button>
      </div>
    </div>

    <div
      style="
        background: #2a2a2a;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #444;
      "
    >
      <h6 style="color: #0066cc; margin-bottom: 12px">Navigation</h6>
      <div style="margin-bottom: 10px">
        <a
          href="{{ url_for('index') }}"
          class="tool-button"
          style="
            display: inline-block;
            text-decoration: none;
            width: 100%;
            text-align: center;
          "
        >
          <i class="fas fa-arrow-left"></i> Back to Studies
        </a>
      </div>
      {% if not is_shared %}
      <div style="margin-bottom: 10px">
        <a
          href="{{ url_for('share_study', study_uid=study.study_uid) }}"
          class="tool-button"
          style="
            display: inline-block;
            text-decoration: none;
            width: 100%;
            text-align: center;
          "
        >
          <i class="fas fa-share"></i> Share Study
        </a>
      </div>
      {% endif %}
    </div>
  </div>

  <!-- Main Viewer Area -->
  <div class="viewer-main">
    <!-- Toolbar -->
    <div class="viewer-toolbar">
      <button class="tool-button" id="panTool" onclick="setActiveTool('Pan')">
        <i class="fas fa-hand-paper"></i> Pan
      </button>
      <button class="tool-button" id="zoomTool" onclick="setActiveTool('Zoom')">
        <i class="fas fa-search-plus"></i> Zoom
      </button>
      <button
        class="tool-button"
        id="windowLevelTool"
        onclick="setActiveTool('WindowLevel')"
      >
        <i class="fas fa-adjust"></i> W/L
      </button>
      <button
        class="tool-button"
        id="lengthTool"
        onclick="setActiveTool('Length')"
      >
        <i class="fas fa-ruler"></i> Length
      </button>
      <button
        class="tool-button"
        id="angleTool"
        onclick="setActiveTool('Angle')"
      >
        <i class="fas fa-drafting-compass"></i> Angle
      </button>
      <button
        class="tool-button"
        id="rectangleTool"
        onclick="setActiveTool('RectangleROI')"
      >
        <i class="fas fa-square"></i> ROI
      </button>
      <button class="tool-button" onclick="resetView()">
        <i class="fas fa-undo"></i> Reset
      </button>
      <button class="tool-button" onclick="invertImage()">
        <i class="fas fa-adjust"></i> Invert
      </button>
    </div>

    <!-- Viewport -->
    <div class="viewer-content">
      <div id="viewport-container" class="viewport-container">
        <div
          id="image-container"
          style="
            position: relative;
            width: 100%;
            height: 100%;
            background: #000;
          "
        >
          <img
            id="dicom-image"
            style="
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
              display: block;
              margin: auto;
            "
          />
          <canvas
            id="annotation-canvas"
            style="
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              pointer-events: none;
            "
          ></canvas>
          <canvas
            id="interaction-canvas"
            style="
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              cursor: crosshair;
            "
            data-study-uid="{{ study.study_uid }}"
          ></canvas>
        </div>
        <div
          id="loading-overlay"
          class="loading-overlay"
          style="display: block"
        >
          <div class="loading-content">
            <div class="spinner"></div>
            <p>Loading DICOM image...</p>
          </div>
        </div>
        <div
          id="error-overlay"
          class="loading-overlay"
          style="display: none; background: rgba(255, 0, 0, 0.1)"
        >
          <div class="loading-content">
            <h6 style="color: #ff6b6b">Error Loading Image</h6>
            <p id="error-message">Failed to load DICOM image</p>
            <button
              onclick="location.reload()"
              style="
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
              "
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %} {% block scripts %}
<script>
  // Simple but Functional Medical DICOM Viewer
  let currentTool = "WindowLevel";
  let isMouseDown = false;
  let lastMousePos = { x: 0, y: 0 };
  let imageTransform = {
    scale: 1,
    translateX: 0,
    translateY: 0,
    windowWidth: 400,
    windowCenter: 40,
    brightness: 0,
    contrast: 100,
    invert: false,
  };
  let measurements = [];
  let currentMeasurement = null;

  let imageElement = null;
  let canvas = null;
  let ctx = null;
  let annotationCanvas = null;
  let annotationCtx = null;

  // Initialize viewer when DOM is loaded
  document.addEventListener("DOMContentLoaded", function () {
    console.log("Initializing DICOM Viewer...");

    const canvas = document.getElementById("interaction-canvas");
    if (canvas) {
      const studyUid = canvas.getAttribute("data-study-uid");
      if (studyUid) {
        initViewer(studyUid);
      } else {
        showError("No study UID found");
      }
    } else {
      showError("Canvas element not found");
    }
  });

  async function initViewer(studyUid) {
    try {
      console.log("Loading DICOM image for study:", studyUid);

      // Get DOM elements
      imageElement = document.getElementById("dicom-image");
      canvas = document.getElementById("interaction-canvas");
      annotationCanvas = document.getElementById("annotation-canvas");

      if (!imageElement || !canvas || !annotationCanvas) {
        throw new Error("Required DOM elements not found");
      }

      ctx = canvas.getContext("2d");
      annotationCtx = annotationCanvas.getContext("2d");

      // Load DICOM image
      await loadDicomImage(studyUid);

      // Setup canvas and event listeners
      setupCanvas();
      setupEventListeners();

      // Initialize window/level controls
      updateWindowLevelControls();

      // Hide loading overlay
      document.getElementById("loading-overlay").style.display = "none";

      console.log("DICOM Viewer initialized successfully");
    } catch (error) {
      console.error("Error initializing viewer:", error);
      showError(`Failed to initialize viewer: ${error.message}`);
    }
  }

  async function loadDicomImage(studyUid) {
    try {
      console.log("Fetching image data...");
      const response = await fetch(`/api/dicom-image/${studyUid}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const imageData = await response.json();
      console.log("Image data received:", imageData);

      if (!imageData.image) {
        throw new Error("No image data in response");
      }

      return new Promise((resolve, reject) => {
        imageElement.onload = function () {
          console.log("Image loaded successfully");
          console.log(
            "Image dimensions:",
            this.naturalWidth,
            "x",
            this.naturalHeight
          );
          applyImageTransform();
          resolve();
        };

        imageElement.onerror = function () {
          console.error("Failed to load image");
          reject(new Error("Failed to load image"));
        };

        console.log(
          "Setting image source:",
          imageData.image.substring(0, 50) + "..."
        );
        imageElement.src = imageData.image;
      });
    } catch (error) {
      console.error("Error loading DICOM image:", error);
      throw error;
    }
  }

  function setupCanvas() {
    const container = document.getElementById("image-container");
    const rect = container.getBoundingClientRect();

    // Set canvas size to match container
    canvas.width = rect.width;
    canvas.height = rect.height;
    annotationCanvas.width = rect.width;
    annotationCanvas.height = rect.height;

    console.log("Canvas setup:", rect.width, "x", rect.height);
  }

  function setupEventListeners() {
    // Mouse events
    canvas.addEventListener("mousedown", handleMouseDown);
    canvas.addEventListener("mousemove", handleMouseMove);
    canvas.addEventListener("mouseup", handleMouseUp);
    canvas.addEventListener("wheel", handleWheel);
    canvas.addEventListener("contextmenu", (e) => e.preventDefault());

    console.log("Event listeners setup complete");
  }

  function handleMouseDown(e) {
    isMouseDown = true;
    const rect = canvas.getBoundingClientRect();
    lastMousePos = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    if (currentTool === "Length" || currentTool === "Angle") {
      startMeasurement(lastMousePos);
    }
  }

  function handleMouseMove(e) {
    if (!isMouseDown) return;

    const rect = canvas.getBoundingClientRect();
    const currentPos = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    const deltaX = currentPos.x - lastMousePos.x;
    const deltaY = currentPos.y - lastMousePos.y;

    switch (currentTool) {
      case "Pan":
        imageTransform.translateX += deltaX;
        imageTransform.translateY += deltaY;
        applyImageTransform();
        break;

      case "WindowLevel":
        // Window width (horizontal movement)
        imageTransform.windowWidth += deltaX * 2;
        imageTransform.windowWidth = Math.max(
          1,
          Math.min(4000, imageTransform.windowWidth)
        );

        // Window center (vertical movement)
        imageTransform.windowCenter -= deltaY * 2;
        imageTransform.windowCenter = Math.max(
          -1000,
          Math.min(3000, imageTransform.windowCenter)
        );

        applyImageTransform();
        updateWindowLevelControls();
        break;

      case "Length":
      case "Angle":
        if (currentMeasurement) {
          updateMeasurement(currentPos);
        }
        break;
    }

    lastMousePos = currentPos;
  }

  function handleMouseUp(e) {
    isMouseDown = false;

    if (currentTool === "Length" || currentTool === "Angle") {
      finishMeasurement();
    }
  }

  function handleWheel(e) {
    e.preventDefault();

    if (currentTool === "Zoom" || e.ctrlKey) {
      const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
      imageTransform.scale *= scaleFactor;
      imageTransform.scale = Math.max(0.1, Math.min(10, imageTransform.scale));
      applyImageTransform();
    }
  }

  function applyImageTransform() {
    if (!imageElement) return;

    const {
      scale,
      translateX,
      translateY,
      windowWidth,
      windowCenter,
      brightness,
      contrast,
      invert,
    } = imageTransform;

    // Apply CSS transforms
    imageElement.style.transform = `scale(${scale}) translate(${translateX}px, ${translateY}px)`;

    // Apply window/level as CSS filters
    const brightnessValue = 100 + brightness + (windowCenter - 40) * 0.5;
    const contrastValue = contrast + (windowWidth - 400) * 0.1;
    const invertValue = invert ? 1 : 0;

    imageElement.style.filter = `brightness(${brightnessValue}%) contrast(${contrastValue}%) invert(${invertValue})`;
  }

  function updateWindowLevelControls() {
    const windowWidthSlider = document.getElementById("windowWidth");
    const windowCenterSlider = document.getElementById("windowCenter");
    const windowWidthValue = document.getElementById("windowWidthValue");
    const windowCenterValue = document.getElementById("windowCenterValue");

    if (windowWidthSlider) {
      windowWidthSlider.value = imageTransform.windowWidth;
      if (windowWidthValue)
        windowWidthValue.textContent = Math.round(imageTransform.windowWidth);
    }

    if (windowCenterSlider) {
      windowCenterSlider.value = imageTransform.windowCenter;
      if (windowCenterValue)
        windowCenterValue.textContent = Math.round(imageTransform.windowCenter);
    }
  }

  // Measurement functions
  function startMeasurement(startPos) {
    currentMeasurement = {
      type: currentTool,
      points: [startPos],
      id: Date.now(),
    };
  }

  function updateMeasurement(currentPos) {
    if (!currentMeasurement) return;

    if (currentMeasurement.type === "Length") {
      currentMeasurement.points = [currentMeasurement.points[0], currentPos];
    } else if (currentMeasurement.type === "Angle") {
      if (currentMeasurement.points.length === 1) {
        currentMeasurement.points.push(currentPos);
      } else if (currentMeasurement.points.length === 2) {
        currentMeasurement.points[2] = currentPos;
      }
    }

    drawMeasurements();
  }

  function finishMeasurement() {
    if (currentMeasurement && currentMeasurement.points.length >= 2) {
      measurements.push({ ...currentMeasurement });
      console.log("Measurement added:", currentMeasurement);
    }
    currentMeasurement = null;
    drawMeasurements();
  }

  function drawMeasurements() {
    if (!annotationCtx) return;

    // Clear previous annotations
    annotationCtx.clearRect(
      0,
      0,
      annotationCanvas.width,
      annotationCanvas.height
    );

    // Draw all measurements
    [...measurements, currentMeasurement]
      .filter(Boolean)
      .forEach((measurement) => {
        drawSingleMeasurement(measurement);
      });
  }

  function drawSingleMeasurement(measurement) {
    const ctx = annotationCtx;
    ctx.strokeStyle = "#00ff00";
    ctx.fillStyle = "#00ff00";
    ctx.lineWidth = 2;
    ctx.font = "14px Arial";

    if (measurement.type === "Length" && measurement.points.length >= 2) {
      const [start, end] = measurement.points;

      // Draw line
      ctx.beginPath();
      ctx.moveTo(start.x, start.y);
      ctx.lineTo(end.x, end.y);
      ctx.stroke();

      // Draw endpoints
      ctx.fillRect(start.x - 3, start.y - 3, 6, 6);
      ctx.fillRect(end.x - 3, end.y - 3, 6, 6);

      // Calculate and display length
      const length = Math.sqrt(
        Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2)
      );
      const midX = (start.x + end.x) / 2;
      const midY = (start.y + end.y) / 2;

      ctx.fillText(`${length.toFixed(1)}px`, midX + 5, midY - 5);
    }

    if (measurement.type === "Angle" && measurement.points.length >= 3) {
      const [p1, p2, p3] = measurement.points;

      // Draw lines
      ctx.beginPath();
      ctx.moveTo(p1.x, p1.y);
      ctx.lineTo(p2.x, p2.y);
      ctx.lineTo(p3.x, p3.y);
      ctx.stroke();

      // Draw points
      [p1, p2, p3].forEach((point) => {
        ctx.fillRect(point.x - 3, point.y - 3, 6, 6);
      });

      // Calculate and display angle
      const angle1 = Math.atan2(p1.y - p2.y, p1.x - p2.x);
      const angle2 = Math.atan2(p3.y - p2.y, p3.x - p2.x);
      let angle = (Math.abs(angle1 - angle2) * 180) / Math.PI;
      if (angle > 180) angle = 360 - angle;

      ctx.fillText(`${angle.toFixed(1)}°`, p2.x + 10, p2.y - 10);
    }
  }

  // Global functions for toolbar buttons
  function setActiveTool(toolName) {
    currentTool = toolName;

    // Update button states
    document
      .querySelectorAll(".tool-button")
      .forEach((btn) => btn.classList.remove("active"));
    const buttonId = toolName.toLowerCase() + "Tool";
    const button = document.getElementById(buttonId);
    if (button) {
      button.classList.add("active");
    }

    // Update cursor
    if (canvas) {
      switch (toolName) {
        case "Pan":
          canvas.style.cursor = "move";
          break;
        case "Zoom":
          canvas.style.cursor = "zoom-in";
          break;
        case "WindowLevel":
          canvas.style.cursor = "crosshair";
          break;
        case "Length":
        case "Angle":
        case "RectangleROI":
          canvas.style.cursor = "crosshair";
          break;
        default:
          canvas.style.cursor = "default";
      }
    }

    console.log(`Activated tool: ${toolName}`);
  }

  // Window/Level functions
  function updateWindowLevel() {
    const windowWidthSlider = document.getElementById("windowWidth");
    const windowCenterSlider = document.getElementById("windowCenter");

    if (windowWidthSlider && windowCenterSlider) {
      imageTransform.windowWidth = parseInt(windowWidthSlider.value);
      imageTransform.windowCenter = parseInt(windowCenterSlider.value);
      applyImageTransform();
      updateWindowLevelControls();
    }
  }

  function applyPreset(presetName) {
    const presets = {
      lung: { width: 1500, center: -600 },
      bone: { width: 2000, center: 300 },
      brain: { width: 100, center: 50 },
      abdomen: { width: 350, center: 50 },
    };

    const preset = presets[presetName];
    if (preset) {
      imageTransform.windowWidth = preset.width;
      imageTransform.windowCenter = preset.center;
      applyImageTransform();
      updateWindowLevelControls();
    }
  }

  // Utility functions
  function resetView() {
    imageTransform = {
      scale: 1,
      translateX: 0,
      translateY: 0,
      windowWidth: 400,
      windowCenter: 40,
      brightness: 0,
      contrast: 100,
      invert: false,
    };

    applyImageTransform();
    updateWindowLevelControls();

    // Clear measurements
    measurements = [];
    drawMeasurements();
  }

  function invertImage() {
    imageTransform.invert = !imageTransform.invert;
    applyImageTransform();
  }

  function showError(message) {
    console.error("Viewer Error:", message);
    document.getElementById("loading-overlay").style.display = "none";
    document.getElementById("error-overlay").style.display = "block";
    document.getElementById("error-message").textContent = message;
  }

  // Set default tool after initialization
  setTimeout(() => {
    setActiveTool("WindowLevel");
  }, 1000);
</script>

{% endblock %}
