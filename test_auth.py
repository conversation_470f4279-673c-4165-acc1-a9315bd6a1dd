#!/usr/bin/env python3
"""
Test script to verify authentication system is working
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_login():
    """Test login functionality"""
    print("🧪 Testing Authentication System...")
    print("=" * 50)
    
    # Test accessing protected route without login
    print("1. Testing protected route access without login...")
    response = requests.get(f"{BASE_URL}/")
    if response.status_code == 200 and "login" in response.url:
        print("✓ Correctly redirected to login page")
    else:
        print(f"❌ Expected redirect to login, got: {response.status_code}")
    
    # Test login page
    print("\n2. Testing login page...")
    response = requests.get(f"{BASE_URL}/login")
    if response.status_code == 200:
        print("✓ Login page loads successfully")
    else:
        print(f"❌ Login page failed: {response.status_code}")
    
    # Test admin user management page (should redirect to login)
    print("\n3. Testing admin pages without authentication...")
    response = requests.get(f"{BASE_URL}/admin/users")
    if response.status_code == 200 and "login" in response.url:
        print("✓ Admin pages correctly protected")
    else:
        print(f"❌ Admin pages not properly protected: {response.status_code}")
    
    print("\n✅ Authentication system tests completed!")
    print("\nTo test login functionality:")
    print("1. Open http://127.0.0.1:5000/login in your browser")
    print("2. Use credentials: admin / admin123")
    print("3. You should be redirected to the dashboard")

def test_api_endpoints():
    """Test if all endpoints are properly configured"""
    print("\n🔗 Testing API Endpoints...")
    print("=" * 50)
    
    endpoints = [
        ("/login", "Login page"),
        ("/admin", "Admin dashboard"),
        ("/admin/users", "User management"),
        ("/register", "User registration"),
        ("/profile", "User profile"),
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", allow_redirects=False)
            if response.status_code in [200, 302]:
                print(f"✓ {description}: {response.status_code}")
            else:
                print(f"❌ {description}: {response.status_code}")
        except Exception as e:
            print(f"❌ {description}: Error - {str(e)}")

if __name__ == '__main__':
    try:
        test_login()
        test_api_endpoints()
        
        print("\n" + "=" * 60)
        print("🎉 USER MANAGEMENT SYSTEM READY!")
        print("=" * 60)
        print("Default Admin Credentials:")
        print("Username: admin")
        print("Password: admin123")
        print("Email: <EMAIL>")
        print("\nSample User Credentials:")
        print("Username: doctor1 | Password: doctor123")
        print("Username: radiologist1 | Password: radio123")
        print("\n🌐 Access the application at: http://127.0.0.1:5000")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask app. Make sure it's running on port 5000")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
