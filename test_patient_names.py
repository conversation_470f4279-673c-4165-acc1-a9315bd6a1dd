#!/usr/bin/env python3
"""
Test script to verify patient names are displayed instead of "Untitled Study"
"""

import requests
import time

BASE_URL = "http://127.0.0.1:5000"

def login_and_get_session():
    """Login and return session for authenticated requests"""
    session = requests.Session()
    
    # Get login page first
    login_page = session.get(f"{BASE_URL}/login")
    if login_page.status_code != 200:
        print(f"❌ Failed to access login page: {login_page.status_code}")
        return None
    
    # Login with admin credentials
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=True)
    
    if login_response.status_code == 200:
        print("✅ Successfully logged in")
        return session
    else:
        print(f"❌ Login failed: {login_response.status_code}")
        return None

def test_dashboard_display(session):
    """Test if dashboard shows patient names instead of 'Untitled Study'"""
    try:
        response = session.get(f"{BASE_URL}/", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for improvements
            improvements = [
                ('No "Untitled Study"', 'Untitled Study' not in content),
                ('Contains Patient Names', 'patient_name' in content.lower()),
                ('Contains Patient IDs', 'patient_id' in content.lower()),
                ('Shows Study Information', 'study:' in content.lower()),
            ]
            
            print("\n📋 Dashboard Display:")
            print("-" * 40)
            
            all_good = True
            for improvement_name, present in improvements:
                status = "✅" if present else "❌"
                print(f"  {status} {improvement_name}")
                if not present:
                    all_good = False
            
            return all_good
        else:
            print(f"❌ Dashboard access failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard test error: {str(e)}")
        return False

def test_admin_display(session):
    """Test if admin page shows patient names instead of 'Untitled Study'"""
    try:
        response = session.get(f"{BASE_URL}/admin", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for improvements
            improvements = [
                ('No "Untitled Study"', 'Untitled Study' not in content),
                ('Contains Patient Names', 'patient_name' in content.lower()),
                ('Contains Patient IDs', 'patient_id' in content.lower()),
                ('Shows Study Information', 'study:' in content.lower()),
                ('Bulk Delete Features', 'bulk-delete-form' in content),
            ]
            
            print("\n📋 Admin Page Display:")
            print("-" * 40)
            
            all_good = True
            for improvement_name, present in improvements:
                status = "✅" if present else "❌"
                print(f"  {status} {improvement_name}")
                if not present:
                    all_good = False
            
            return all_good
        else:
            print(f"❌ Admin page access failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Admin page test error: {str(e)}")
        return False

def check_database_patient_data():
    """Check if database has patient information"""
    try:
        from app import app, db
        from models import DicomStudy
        
        with app.app_context():
            # Check current database state
            total_studies = DicomStudy.query.count()
            studies_with_patient_name = DicomStudy.query.filter(DicomStudy.patient_name.isnot(None)).filter(DicomStudy.patient_name != '').count()
            studies_with_patient_id = DicomStudy.query.filter(DicomStudy.patient_id.isnot(None)).filter(DicomStudy.patient_id != '').count()
            studies_with_description = DicomStudy.query.filter(DicomStudy.study_description.isnot(None)).filter(DicomStudy.study_description != '').count()
            
            print(f"\n📊 Database Patient Information:")
            print(f"   Total studies: {total_studies}")
            print(f"   Studies with patient names: {studies_with_patient_name}")
            print(f"   Studies with patient IDs: {studies_with_patient_id}")
            print(f"   Studies with descriptions: {studies_with_description}")
            
            if total_studies > 0:
                # Get a sample study to check data
                sample_study = DicomStudy.query.first()
                print(f"\n📋 Sample Study Data:")
                print(f"   Patient Name: {sample_study.patient_name or 'None'}")
                print(f"   Patient ID: {sample_study.patient_id or 'None'}")
                print(f"   Study Description: {sample_study.study_description or 'None'}")
                print(f"   Original Filename: {sample_study.original_filename}")
                
                return True
            else:
                print("⚠️  No studies in database")
                return False
            
    except Exception as e:
        print(f"❌ Database check error: {str(e)}")
        return False

def main():
    """Test patient name display functionality"""
    print("👤 PATIENT NAME DISPLAY TEST")
    print("=" * 50)
    
    # Check database first
    print("🗄️  Checking Database Patient Information...")
    db_ok = check_database_patient_data()
    
    # Login and get session
    session = login_and_get_session()
    if not session:
        print("❌ Cannot proceed without login session")
        return
    
    # Test pages
    print("\n🌐 Testing Page Displays...")
    dashboard_ok = test_dashboard_display(session)
    admin_ok = test_admin_display(session)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    tests = [
        ("Database Patient Data", db_ok),
        ("Dashboard Display", dashboard_ok),
        ("Admin Page Display", admin_ok),
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 PATIENT NAME DISPLAY FIXED!")
        print("\n✅ What's Fixed:")
        print("  • No more 'Untitled Study' labels")
        print("  • Patient names displayed prominently")
        print("  • Patient IDs shown when available")
        print("  • Study descriptions as secondary information")
        print("  • Fallback to filename when no patient data")
        
        print("\n📋 Display Priority (in order):")
        print("  1. Patient Name + Patient ID")
        print("  2. Patient Name only")
        print("  3. Patient ID only")
        print("  4. Study Description")
        print("  5. Original Filename")
        
        print(f"\n🌐 View the improvements at:")
        print(f"  • Dashboard: {BASE_URL}/")
        print(f"  • Admin Page: {BASE_URL}/admin")
        print(f"  • Share Pages: {BASE_URL}/share/[study_uid]")
        
        print("\n🔐 Login as admin: admin / admin123")
        
    else:
        print(f"\n⚠️  {total-passed} tests failed. Check the logs above for details.")
        
    print("\n📋 Next Steps:")
    print("  • Upload new DICOM files to see patient names")
    print("  • Existing files will show improved display")
    print("  • Patient information takes priority over study descriptions")

if __name__ == '__main__':
    main()
