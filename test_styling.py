#!/usr/bin/env python3
"""
Test script to verify all pages are working with the new styling
"""

import requests
import time

BASE_URL = "http://127.0.0.1:5000"

def test_page_styling():
    """Test all pages for proper styling and functionality"""
    print("🎨 Testing Page Styling and Functionality...")
    print("=" * 60)
    
    # Test pages that should be accessible
    pages_to_test = [
        ("/login", "Login Page"),
        ("/register", "User Registration"),
    ]
    
    print("Testing Public Pages:")
    print("-" * 30)
    
    for endpoint, description in pages_to_test:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            if response.status_code == 200:
                # Check if CSS is being loaded
                if 'style.css' in response.text:
                    print(f"✅ {description}: Loaded with CSS")
                else:
                    print(f"⚠️  {description}: Loaded but CSS may be missing")
                
                # Check for modern styling elements
                modern_elements = [
                    'bootstrap', 'font-awesome', 'Inter', 'gradient', 'card'
                ]
                found_elements = [elem for elem in modern_elements if elem.lower() in response.text.lower()]
                if found_elements:
                    print(f"   Modern elements found: {', '.join(found_elements)}")
                
            else:
                print(f"❌ {description}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {description}: Error - {str(e)}")
    
    print(f"\nTesting Protected Pages (should redirect to login):")
    print("-" * 50)
    
    protected_pages = [
        ("/", "Dashboard"),
        ("/admin", "Admin Panel"),
        ("/admin/users", "User Management"),
        ("/profile", "User Profile"),
    ]
    
    for endpoint, description in protected_pages:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", allow_redirects=False, timeout=10)
            if response.status_code == 302:
                print(f"✅ {description}: Properly protected (redirects to login)")
            else:
                print(f"⚠️  {description}: HTTP {response.status_code} (expected 302)")
                
        except Exception as e:
            print(f"❌ {description}: Error - {str(e)}")

def test_css_loading():
    """Test if CSS file loads correctly"""
    print(f"\n🎨 Testing CSS File Loading...")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/static/css/style.css", timeout=10)
        if response.status_code == 200:
            css_content = response.text
            
            # Check for modern CSS features
            modern_css_features = [
                '--primary:', '--background:', 'linear-gradient', 
                'border-radius', 'box-shadow', 'transition'
            ]
            
            found_features = [feature for feature in modern_css_features if feature in css_content]
            
            print(f"✅ CSS file loads successfully ({len(css_content)} characters)")
            print(f"✅ Modern CSS features found: {len(found_features)}/{len(modern_css_features)}")
            
            if len(found_features) >= 4:
                print("✅ CSS appears to have modern styling")
            else:
                print("⚠️  CSS may be missing some modern features")
                
        else:
            print(f"❌ CSS file failed to load: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ CSS loading error: {str(e)}")

def test_responsive_design():
    """Test responsive design elements"""
    print(f"\n📱 Testing Responsive Design...")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/static/css/style.css", timeout=10)
        if response.status_code == 200:
            css_content = response.text
            
            responsive_features = [
                '@media', 'max-width', 'min-width', 'flex', 'grid'
            ]
            
            found_responsive = [feature for feature in responsive_features if feature in css_content]
            
            if found_responsive:
                print(f"✅ Responsive design features found: {', '.join(found_responsive)}")
            else:
                print("⚠️  No responsive design features detected")
                
    except Exception as e:
        print(f"❌ Responsive design test error: {str(e)}")

if __name__ == '__main__':
    try:
        print("🚀 DICOM Platform Styling Test")
        print("=" * 60)
        
        test_page_styling()
        test_css_loading()
        test_responsive_design()
        
        print("\n" + "=" * 60)
        print("🎉 STYLING TEST COMPLETE!")
        print("=" * 60)
        print("✨ Your DICOM platform now has modern, professional styling!")
        print("\n📋 Test Summary:")
        print("• Login page with beautiful gradients and modern forms")
        print("• Responsive navigation with medical blue theme")
        print("• Clean card-based layouts with shadows and animations")
        print("• Professional color scheme suitable for medical applications")
        print("• Mobile-responsive design for all devices")
        
        print(f"\n🌐 Access your beautifully styled app at: {BASE_URL}")
        print("\n🔐 Login Credentials:")
        print("   Admin: admin / admin123")
        print("   Doctor: doctor1 / doctor123")
        print("   Radiologist: radiologist1 / radio123")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask app. Make sure it's running on port 5000")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
