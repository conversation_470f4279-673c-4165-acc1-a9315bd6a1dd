#!/usr/bin/env python3
"""
Test the multi-file upload functionality with real DICOM files
"""

import requests
import os
import time

BASE_URL = "http://127.0.0.1:5000"

def login_and_get_session():
    """Login and return session for authenticated requests"""
    session = requests.Session()
    
    # Get login page first
    login_page = session.get(f"{BASE_URL}/login")
    if login_page.status_code != 200:
        print(f"❌ Failed to access login page: {login_page.status_code}")
        return None
    
    # Login with admin credentials
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=True)
    
    if login_response.status_code == 200 and 'dashboard' in login_response.url.lower():
        print("✅ Successfully logged in")
        return session
    else:
        print(f"❌ Login failed: {login_response.status_code}")
        return None

def test_single_file_upload(session):
    """Test uploading a single DICOM file"""
    print("\n📁 Testing Single File Upload")
    print("-" * 40)
    
    test_file = "test_files/single_test.dcm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        with open(test_file, 'rb') as f:
            files = {'files': (os.path.basename(test_file), f, 'application/dicom')}
            
            response = session.post(f"{BASE_URL}/upload", files=files, allow_redirects=True)
            
            if response.status_code == 200:
                if 'successfully' in response.text.lower():
                    print("✅ Single file upload: SUCCESS")
                    return True
                else:
                    print("⚠️  Single file upload: Uploaded but may have issues")
                    print(f"   Response contains: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ Single file upload failed: HTTP {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Single file upload error: {str(e)}")
        return False

def test_multiple_file_upload(session):
    """Test uploading multiple DICOM files"""
    print("\n📁 Testing Multiple File Upload")
    print("-" * 40)
    
    test_files = [
        "test_files/sample_image_001.dcm",
        "test_files/sample_image_002.dcm"
    ]
    
    # Check if files exist
    missing_files = [f for f in test_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ Test files not found: {missing_files}")
        return False
    
    try:
        files = []
        for test_file in test_files:
            with open(test_file, 'rb') as f:
                files.append(('files', (os.path.basename(test_file), f.read(), 'application/dicom')))
        
        response = session.post(f"{BASE_URL}/upload", files=files, allow_redirects=True)
        
        if response.status_code == 200:
            if 'successfully' in response.text.lower():
                print("✅ Multiple file upload: SUCCESS")
                return True
            else:
                print("⚠️  Multiple file upload: Uploaded but may have issues")
                return False
        else:
            print(f"❌ Multiple file upload failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Multiple file upload error: {str(e)}")
        return False

def test_zip_file_upload(session):
    """Test uploading a ZIP file with multiple DICOM files"""
    print("\n📦 Testing ZIP File Upload")
    print("-" * 40)
    
    zip_file = "test_files/dicom_series.zip"
    
    if not os.path.exists(zip_file):
        print(f"❌ ZIP test file not found: {zip_file}")
        return False
    
    try:
        with open(zip_file, 'rb') as f:
            files = {'files': (os.path.basename(zip_file), f, 'application/zip')}
            
            response = session.post(f"{BASE_URL}/upload", files=files, allow_redirects=True)
            
            if response.status_code == 200:
                if 'gallery' in response.url.lower() or 'successfully' in response.text.lower():
                    print("✅ ZIP file upload: SUCCESS")
                    if 'gallery' in response.url.lower():
                        print("   ✅ Redirected to gallery view")
                    return True
                else:
                    print("⚠️  ZIP file upload: Uploaded but may have issues")
                    return False
            else:
                print(f"❌ ZIP file upload failed: HTTP {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ ZIP file upload error: {str(e)}")
        return False

def test_gallery_access(session):
    """Test accessing the gallery viewer"""
    print("\n🖼️  Testing Gallery Access")
    print("-" * 40)
    
    try:
        # Try to access a gallery (we'll use a test series UID)
        response = session.get(f"{BASE_URL}/gallery/test-series-uid", allow_redirects=True)
        
        if response.status_code == 200:
            if 'gallery' in response.text.lower():
                print("✅ Gallery template: ACCESSIBLE")
                return True
            else:
                print("⚠️  Gallery accessible but may not be the right template")
                return False
        elif response.status_code == 404:
            print("✅ Gallery endpoint exists (404 expected for non-existent series)")
            return True
        else:
            print(f"❌ Gallery access failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Gallery access error: {str(e)}")
        return False

def test_api_endpoints(session):
    """Test DICOM API endpoints"""
    print("\n🔌 Testing API Endpoints")
    print("-" * 40)
    
    try:
        # Test DICOM image API
        response = session.get(f"{BASE_URL}/api/dicom-image/test-uid", allow_redirects=False)
        
        if response.status_code in [404, 500]:  # Expected for non-existent UID
            print("✅ DICOM image API: ENDPOINT EXISTS")
            return True
        else:
            print(f"⚠️  DICOM image API: Unexpected response {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API endpoint test error: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 MULTI-FILE DICOM UPLOAD FUNCTIONALITY TEST")
    print("=" * 60)
    
    # Check if test files exist
    if not os.path.exists("test_files"):
        print("❌ Test files directory not found. Run create_test_files.py first.")
        return
    
    # Login and get session
    session = login_and_get_session()
    if not session:
        print("❌ Cannot proceed without login session")
        return
    
    # Run tests
    tests = [
        ("Single File Upload", test_single_file_upload),
        ("Multiple File Upload", test_multiple_file_upload),
        ("ZIP File Upload", test_zip_file_upload),
        ("Gallery Access", test_gallery_access),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func(session)
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Your multi-file upload system is working!")
        print("\n📋 What you can now do:")
        print("  • Upload single DICOM files")
        print("  • Upload multiple DICOM files at once")
        print("  • Upload ZIP archives with multiple DICOM files")
        print("  • View multiple images in gallery mode")
        print("  • Compare images side by side")
        
        print(f"\n🌐 Access your enhanced DICOM platform at: {BASE_URL}")
        
    else:
        print(f"\n⚠️  {total-passed} tests failed. Check the logs above for details.")

if __name__ == '__main__':
    main()
