<!DOCTYPE html>
<html>
<head>
    <title>Test DICOM Viewer</title>
    <style>
        #dicom-image {
            width: 600px;
            height: 400px;
            border: 2px solid #ccc;
            background-color: #f0f0f0;
        }
        .alert {
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .spinner-border {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            vertical-align: text-bottom;
            border: 0.25em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border 0.75s linear infinite;
        }
        @keyframes spinner-border {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>DICOM Viewer Test</h1>
    <div id="dicom-image" data-study-uid="50ea642e-d662-4c2d-b4ac-b489f81c7ed8"></div>
    
    <div id="debug-info">
        <h3>Debug Information:</h3>
        <div id="debug-log"></div>
    </div>

    <script>
        // Debug logging function
        function debugLog(message) {
            console.log(message);
            const debugDiv = document.getElementById('debug-log');
            debugDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
        }

        // Simple DICOM Viewer (standalone version)
        class SimpleDicomViewer {
            constructor(elementId, studyUid) {
                this.element = document.getElementById(elementId);
                this.studyUid = studyUid;
                debugLog('SimpleDicomViewer initialized with study: ' + studyUid);
                this.init();
            }

            async init() {
                try {
                    debugLog('Starting simple viewer initialization...');
                    
                    // Create loading indicator
                    this.element.innerHTML = `
                        <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
                            <div style="text-align: center;">
                                <div class="spinner-border" role="status"></div>
                                <p style="margin-top: 10px;">Loading DICOM image...</p>
                            </div>
                        </div>
                    `;

                    // Load image data from API
                    const url = `/api/dicom-image/${this.studyUid}`;
                    debugLog('Fetching from: ' + url);
                    
                    const response = await fetch(url);
                    debugLog('Response status: ' + response.status);
                    
                    if (!response.ok) {
                        throw new Error(`Failed to load image: ${response.status} ${response.statusText}`);
                    }

                    const imageData = await response.json();
                    debugLog('Image data received, keys: ' + Object.keys(imageData).join(', '));
                    
                    this.displayImage(imageData);
                    
                } catch (error) {
                    debugLog('Error in simple viewer: ' + error.message);
                    this.showError(`Failed to load image: ${error.message}`);
                }
            }

            displayImage(imageData) {
                debugLog('Displaying image...');
                this.element.innerHTML = `
                    <div style="text-align: center;">
                        <img src="${imageData.image}" 
                             style="max-width: 100%; max-height: 100%; object-fit: contain;"
                             alt="DICOM Image"
                             onload="debugLog('Image loaded successfully')"
                             onerror="debugLog('Image failed to load')">
                    </div>
                `;
                debugLog('Image HTML set');
            }

            showError(message) {
                debugLog('Showing error: ' + message);
                this.element.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>Viewer Error</h6>
                        <p>${message}</p>
                        <small>Please check the console for more details.</small>
                    </div>
                `;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM loaded, initializing viewer...');
            const element = document.getElementById('dicom-image');
            if (element) {
                const studyUid = element.getAttribute('data-study-uid');
                debugLog('Found element with study UID: ' + studyUid);
                if (studyUid) {
                    window.viewer = new SimpleDicomViewer('dicom-image', studyUid);
                } else {
                    debugLog('No study UID found');
                }
            } else {
                debugLog('No dicom-image element found');
            }
        });
    </script>
</body>
</html>
