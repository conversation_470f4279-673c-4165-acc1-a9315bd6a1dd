import os
import pydicom
import numpy as np
import base64
from PIL import Image
import io
import logging

class DicomProcessor:
    """Handle DICOM file processing and image extraction"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def validate_dicom(self, filepath):
        """Validate if file is a valid DICOM file"""
        try:
            # Check if file exists and has content
            if not os.path.exists(filepath):
                self.logger.error(f"File does not exist: {filepath}")
                return False

            file_size = os.path.getsize(filepath)
            if file_size < 132:  # Minimum DICOM file size (128 byte preamble + DICM)
                self.logger.error(f"File too small to be DICOM: {file_size} bytes")
                return False

            # Try to read the DICOM file
            ds = pydicom.dcmread(filepath, force=True)  # Use force=True for more lenient parsing

            # Basic validation - check for essential DICOM elements
            if not hasattr(ds, 'SOPClassUID') and not hasattr(ds, 'Modality'):
                self.logger.warning(f"File may not be a standard DICOM but will attempt to process: {filepath}")

            return True

        except Exception as e:
            self.logger.error(f"DICOM validation failed for {filepath}: {str(e)}")
            return False

    def extract_metadata(self, filepath):
        """Extract metadata from DICOM file including patient information"""
        try:
            ds = pydicom.dcmread(filepath)
            metadata = {}

            # Study and technical metadata
            metadata['StudyDate'] = getattr(ds, 'StudyDate', '')
            metadata['StudyDescription'] = getattr(ds, 'StudyDescription', '')
            metadata['Modality'] = getattr(ds, 'Modality', '')
            metadata['InstitutionName'] = getattr(ds, 'InstitutionName', '')
            metadata['Manufacturer'] = getattr(ds, 'Manufacturer', '')
            metadata['SeriesDescription'] = getattr(ds, 'SeriesDescription', '')
            metadata['PatientPosition'] = getattr(ds, 'PatientPosition', '')
            metadata['SliceThickness'] = getattr(ds, 'SliceThickness', '')
            metadata['Rows'] = getattr(ds, 'Rows', 0)
            metadata['Columns'] = getattr(ds, 'Columns', 0)

            # Patient information (for medical collaboration)
            metadata['PatientName'] = str(getattr(ds, 'PatientName', ''))
            metadata['PatientID'] = getattr(ds, 'PatientID', '')
            metadata['PatientBirthDate'] = getattr(ds, 'PatientBirthDate', '')
            metadata['PatientSex'] = getattr(ds, 'PatientSex', '')
            metadata['PatientAge'] = getattr(ds, 'PatientAge', '')
            metadata['PatientWeight'] = getattr(ds, 'PatientWeight', '')

            # Physician information
            metadata['ReferringPhysicianName'] = str(getattr(ds, 'ReferringPhysicianName', ''))
            metadata['PerformingPhysicianName'] = str(getattr(ds, 'PerformingPhysicianName', ''))
            metadata['RequestingPhysician'] = str(getattr(ds, 'RequestingPhysician', ''))

            # Study details
            metadata['StudyTime'] = getattr(ds, 'StudyTime', '')
            metadata['AccessionNumber'] = getattr(ds, 'AccessionNumber', '')
            metadata['StudyID'] = getattr(ds, 'StudyID', '')

            return metadata
        except Exception as e:
            self.logger.error(f"Metadata extraction failed: {str(e)}")
            return {}

    def get_image_data(self, filepath):
        """Extract image data from DICOM file for web viewing"""
        try:
            ds = pydicom.dcmread(filepath, force=True)

            # Get pixel array
            if not hasattr(ds, 'pixel_array'):
                # For test files without pixel data, create a placeholder image
                self.logger.warning(f"DICOM file does not contain pixel data, creating placeholder: {filepath}")
                return self._create_placeholder_image(ds, filepath)

            pixel_array = ds.pixel_array

            # Handle different bit depths and photometric interpretations
            if len(pixel_array.shape) == 3:
                # Color image
                image_array = pixel_array
            else:
                # Grayscale image - normalize to 8-bit
                image_array = self._normalize_to_8bit(pixel_array)

            # Convert to PIL Image
            if len(image_array.shape) == 2:
                # Grayscale
                pil_image = Image.fromarray(image_array, mode='L')
            else:
                # RGB
                pil_image = Image.fromarray(image_array, mode='RGB')

            # Convert to base64 for web display
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            # Prepare response data
            image_data = {
                'image': f"data:image/png;base64,{img_str}",
                'width': int(getattr(ds, 'Columns', pil_image.width)),
                'height': int(getattr(ds, 'Rows', pil_image.height)),
                'metadata': {
                    'study_date': getattr(ds, 'StudyDate', ''),
                    'study_description': getattr(ds, 'StudyDescription', ''),
                    'modality': getattr(ds, 'Modality', ''),
                    'series_description': getattr(ds, 'SeriesDescription', ''),
                    'patient_position': getattr(ds, 'PatientPosition', ''),
                    'slice_thickness': str(getattr(ds, 'SliceThickness', '')),
                    'institution': getattr(ds, 'InstitutionName', ''),
                    'manufacturer': getattr(ds, 'Manufacturer', ''),
                    'rows': int(getattr(ds, 'Rows', 0)),
                    'columns': int(getattr(ds, 'Columns', 0))
                }
            }

            return image_data

        except Exception as e:
            self.logger.error(f"Image data extraction failed: {str(e)}")
            raise e

    def _normalize_to_8bit(self, pixel_array):
        """Normalize pixel array to 8-bit for display"""
        try:
            # Handle different data types
            if pixel_array.dtype == np.uint8:
                return pixel_array

            # Normalize to 0-255 range
            array_min = np.min(pixel_array)
            array_max = np.max(pixel_array)

            if array_max == array_min:
                return np.zeros_like(pixel_array, dtype=np.uint8)

            normalized = ((pixel_array - array_min) / (array_max - array_min) * 255).astype(np.uint8)
            return normalized

        except Exception as e:
            self.logger.error(f"Normalization failed: {str(e)}")
            # Return a black image as fallback
            return np.zeros_like(pixel_array, dtype=np.uint8)

    def _create_placeholder_image(self, ds, filepath):
        """Create a placeholder image for DICOM files without pixel data"""
        try:
            # Create a simple placeholder image
            width = int(getattr(ds, 'Columns', 512))
            height = int(getattr(ds, 'Rows', 512))

            # Ensure reasonable dimensions
            if width <= 0 or width > 2048:
                width = 512
            if height <= 0 or height > 2048:
                height = 512

            # Create a gradient placeholder image
            placeholder = np.zeros((height, width), dtype=np.uint8)

            # Add some pattern to make it visible
            for i in range(height):
                for j in range(width):
                    placeholder[i, j] = (i + j) % 256

            # Convert to PIL Image
            pil_image = Image.fromarray(placeholder, mode='L')

            # Add text overlay
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(pil_image)

            try:
                # Try to use a default font
                font = ImageFont.load_default()
            except:
                font = None

            # Add text
            text_lines = [
                "DICOM Test File",
                f"Patient: {getattr(ds, 'PatientName', 'Test Patient')}",
                f"Modality: {getattr(ds, 'Modality', 'CT')}",
                f"Study: {getattr(ds, 'StudyDescription', 'Test Study')}",
                f"File: {os.path.basename(filepath)}"
            ]

            y_offset = 50
            for line in text_lines:
                draw.text((50, y_offset), str(line), fill=255, font=font)
                y_offset += 30

            # Convert to base64
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            # Prepare response data
            image_data = {
                'image': img_str,
                'width': width,
                'height': height,
                'metadata': {
                    'study_date': getattr(ds, 'StudyDate', ''),
                    'study_description': getattr(ds, 'StudyDescription', 'Test Study'),
                    'modality': getattr(ds, 'Modality', 'CT'),
                    'series_description': getattr(ds, 'SeriesDescription', 'Test Series'),
                    'patient_position': getattr(ds, 'PatientPosition', ''),
                    'slice_thickness': str(getattr(ds, 'SliceThickness', '')),
                    'institution': getattr(ds, 'InstitutionName', 'Test Hospital'),
                    'manufacturer': getattr(ds, 'Manufacturer', 'Test Manufacturer'),
                    'rows': height,
                    'columns': width
                },
                'is_placeholder': True
            }

            return image_data

        except Exception as e:
            self.logger.error(f"Failed to create placeholder image: {str(e)}")
            # Return minimal response
            return {
                'error': 'Failed to process DICOM file',
                'message': str(e),
                'filepath': filepath
            }
