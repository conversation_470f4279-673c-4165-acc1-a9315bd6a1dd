#!/usr/bin/env python3
"""
Multi-file processor for handling multiple DICOM files, ZIP, and RAR archives
"""

import os
import zipfile
import rarfile
import tempfile
import shutil
import logging
from typing import List, Dict, Tuple, Optional
from werkzeug.utils import secure_filename
from datetime import datetime

class MultiFileProcessor:
    """Handle multiple DICOM files and archive extraction"""
    
    def __init__(self, upload_folder: str):
        self.upload_folder = upload_folder
        self.logger = logging.getLogger(__name__)
        
        # Supported file extensions
        self.dicom_extensions = {'.dcm', '.dicom', '.dic'}
        self.archive_extensions = {'.zip', '.rar'}
        self.supported_extensions = self.dicom_extensions | self.archive_extensions
        
    def is_supported_file(self, filename: str) -> bool:
        """Check if file is supported"""
        ext = os.path.splitext(filename.lower())[1]
        return ext in self.supported_extensions
    
    def is_dicom_file(self, filename: str) -> bool:
        """Check if file is a DICOM file"""
        ext = os.path.splitext(filename.lower())[1]
        return ext in self.dicom_extensions
    
    def is_archive_file(self, filename: str) -> bool:
        """Check if file is an archive file"""
        ext = os.path.splitext(filename.lower())[1]
        return ext in self.archive_extensions
    
    def extract_archive(self, archive_path: str, extract_to: str) -> List[str]:
        """Extract archive and return list of extracted DICOM files"""
        extracted_files = []
        
        try:
            # Determine archive type and extract
            if archive_path.lower().endswith('.zip'):
                extracted_files = self._extract_zip(archive_path, extract_to)
            elif archive_path.lower().endswith('.rar'):
                extracted_files = self._extract_rar(archive_path, extract_to)
            else:
                raise ValueError(f"Unsupported archive format: {archive_path}")
                
            self.logger.info(f"Extracted {len(extracted_files)} files from {archive_path}")
            return extracted_files
            
        except Exception as e:
            self.logger.error(f"Error extracting archive {archive_path}: {str(e)}")
            raise
    
    def _extract_zip(self, zip_path: str, extract_to: str) -> List[str]:
        """Extract ZIP file and return DICOM files"""
        extracted_files = []
        
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # Get list of files in the archive
            file_list = zip_ref.namelist()
            
            for file_info in file_list:
                # Skip directories
                if file_info.endswith('/'):
                    continue
                
                # Check if it's a potential DICOM file
                if self._is_potential_dicom(file_info):
                    try:
                        # Extract file
                        zip_ref.extract(file_info, extract_to)
                        extracted_path = os.path.join(extract_to, file_info)
                        
                        # Normalize path and add to list
                        normalized_path = os.path.normpath(extracted_path)
                        if os.path.exists(normalized_path):
                            extracted_files.append(normalized_path)
                            
                    except Exception as e:
                        self.logger.warning(f"Failed to extract {file_info}: {str(e)}")
                        continue
        
        return extracted_files
    
    def _extract_rar(self, rar_path: str, extract_to: str) -> List[str]:
        """Extract RAR file and return DICOM files"""
        extracted_files = []
        
        try:
            with rarfile.RarFile(rar_path, 'r') as rar_ref:
                # Get list of files in the archive
                file_list = rar_ref.namelist()
                
                for file_info in file_list:
                    # Skip directories
                    if file_info.endswith('/') or file_info.endswith('\\'):
                        continue
                    
                    # Check if it's a potential DICOM file
                    if self._is_potential_dicom(file_info):
                        try:
                            # Extract file
                            rar_ref.extract(file_info, extract_to)
                            extracted_path = os.path.join(extract_to, file_info)
                            
                            # Normalize path and add to list
                            normalized_path = os.path.normpath(extracted_path)
                            if os.path.exists(normalized_path):
                                extracted_files.append(normalized_path)
                                
                        except Exception as e:
                            self.logger.warning(f"Failed to extract {file_info}: {str(e)}")
                            continue
                            
        except rarfile.NotRarFile:
            raise ValueError("Invalid RAR file format")
        except rarfile.BadRarFile:
            raise ValueError("Corrupted RAR file")
        except Exception as e:
            self.logger.error(f"Error extracting RAR file: {str(e)}")
            raise
        
        return extracted_files
    
    def _is_potential_dicom(self, filename: str) -> bool:
        """Check if file might be a DICOM file"""
        # Check by extension first
        if self.is_dicom_file(filename):
            return True
        
        # Check for files without extension (common in DICOM)
        basename = os.path.basename(filename)
        
        # DICOM files often have no extension or numeric names
        if '.' not in basename:
            return True
        
        # Check for common DICOM patterns
        dicom_patterns = [
            'dicom', 'dcm', 'img', 'ima', 'image'
        ]
        
        filename_lower = filename.lower()
        return any(pattern in filename_lower for pattern in dicom_patterns)
    
    def process_multiple_files(self, files: List, upload_folder: str) -> List[Dict]:
        """Process multiple uploaded files"""
        results = []
        temp_dirs = []
        
        try:
            for file in files:
                if not file or file.filename == '':
                    continue
                
                # Create secure filename
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
                filename = f"{timestamp}_{filename}"
                
                # Save uploaded file
                filepath = os.path.join(upload_folder, filename)
                file.save(filepath)
                
                result = {
                    'original_filename': file.filename,
                    'saved_filename': filename,
                    'filepath': filepath,
                    'type': 'unknown',
                    'extracted_files': [],
                    'success': False,
                    'error': None
                }
                
                try:
                    if self.is_dicom_file(file.filename):
                        # Single DICOM file
                        result['type'] = 'dicom'
                        result['extracted_files'] = [filepath]
                        result['success'] = True
                        
                    elif self.is_archive_file(file.filename):
                        # Archive file - extract contents
                        result['type'] = 'archive'
                        
                        # Create temporary extraction directory
                        temp_dir = tempfile.mkdtemp(prefix='dicom_extract_')
                        temp_dirs.append(temp_dir)
                        
                        # Extract archive
                        extracted_files = self.extract_archive(filepath, temp_dir)
                        result['extracted_files'] = extracted_files
                        result['success'] = len(extracted_files) > 0
                        
                        if not result['success']:
                            result['error'] = 'No DICOM files found in archive'
                    
                    else:
                        result['error'] = f'Unsupported file type: {file.filename}'
                        
                except Exception as e:
                    result['error'] = str(e)
                    self.logger.error(f"Error processing file {file.filename}: {str(e)}")
                
                results.append(result)
            
            return results
            
        except Exception as e:
            # Cleanup temp directories on error
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir, ignore_errors=True)
            raise
    
    def cleanup_temp_files(self, temp_dirs: List[str]):
        """Clean up temporary directories"""
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    self.logger.info(f"Cleaned up temp directory: {temp_dir}")
                except Exception as e:
                    self.logger.warning(f"Failed to cleanup temp directory {temp_dir}: {str(e)}")
    
    def get_file_info(self, filepath: str) -> Dict:
        """Get file information"""
        try:
            stat = os.stat(filepath)
            return {
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'exists': True
            }
        except Exception:
            return {
                'size': 0,
                'modified': None,
                'exists': False
            }
